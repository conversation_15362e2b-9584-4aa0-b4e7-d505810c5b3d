2025-06-10 14:46:00,874 - __main__ - INFO - Configuration loaded successfully
2025-06-10 14:46:02,983 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-10 14:46:02,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-10 14:46:10,304 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:10] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:10,923 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:10,928 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:11,445 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:11] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-10 14:46:14,973 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E9A1940>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:14,974 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E99B4D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:14,975 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:14] "[35m[1mGET /api/employees HTTP/1.1[0m" 500 -
2025-06-10 14:46:14,976 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:14] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:14,982 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:19,028 - data_interface.app - ERROR - Error fetching staging data: HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: /api/staging/data?status=staged&limit=50 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C44E99A350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-10 14:46:19,030 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:19] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:23,582 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:23] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:23,648 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:23,650 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:30,669 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:30] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:31,493 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:31,796 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:46:38,688 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:38,689 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:38] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:39,480 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,482 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:39] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:39,648 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,648 - werkzeug - INFO - 127.0.0.1 - - [10/Jun/2025 14:46:39] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:39,872 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:46:39,873 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:39] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:50,102 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET / HTTP/1.1" 200 -
2025-06-10 14:46:50,200 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:50,201 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:50,204 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:46:50,206 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:50] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:55,599 - data_interface.app - INFO - Processing 1 selected records
2025-06-10 14:46:55,601 - automation_service - INFO - Starting automation job auto_20250610_144655
2025-06-10 14:46:55,601 - automation_service - INFO - Started automation job auto_20250610_144655 for 1 records
2025-06-10 14:46:55,602 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:55] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-10 14:46:55,608 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-10 14:46:55,609 - WDM - INFO - ====== WebDriver manager ======
2025-06-10 14:46:56,646 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:56,866 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:56,977 - WDM - INFO - There is no [win64] chromedriver "137.0.7151.68" for browser google-chrome "137.0.7151" in cache
2025-06-10 14:46:56,978 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:46:57,396 - WDM - INFO - WebDriver version 137.0.7151.68 selected
2025-06-10 14:46:57,399 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/win32/chromedriver-win32.zip
2025-06-10 14:46:57,400 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/win32/chromedriver-win32.zip
2025-06-10 14:46:57,612 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:46:57,613 - werkzeug - INFO - ********** - - [10/Jun/2025 14:46:57] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:46:57,669 - WDM - INFO - Driver downloading response is 200
2025-06-10 14:47:00,027 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:47:01,076 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68]
2025-06-10 14:47:03,536 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-10 14:47:03,546 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-10 14:47:03,547 - automation_service - INFO - Created 1 mock staging records
2025-06-10 14:47:03,547 - automation_service - INFO - Processing 1 staging records
2025-06-10 14:47:23,650 - core.staging_automation - INFO - Performing login...
2025-06-10 14:47:23,650 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:47:53,675 - core.staging_automation - ERROR - Login failed: Message: timeout: Timed out receiving message from renderer: 29.155
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb21beb]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2038d]
	(No symbol) [0x0xb2cb99]
	(No symbol) [0x0xb3e265]
	(No symbol) [0x0xb43c96]
	(No symbol) [0x0xb209cd]
	(No symbol) [0x0xb3dfc9]
	(No symbol) [0x0xbbfd65]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:47:53,678 - core.staging_automation - ERROR - Login check failed: Message: timeout: Timed out receiving message from renderer: 29.155
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb21beb]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2038d]
	(No symbol) [0x0xb2cb99]
	(No symbol) [0x0xb3e265]
	(No symbol) [0x0xb43c96]
	(No symbol) [0x0xb209cd]
	(No symbol) [0x0xb3dfc9]
	(No symbol) [0x0xbbfd65]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:47:53,679 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:48:17,378 - core.staging_automation - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-10 14:48:17,379 - core.staging_automation - INFO - Login completed successfully
2025-06-10 14:48:17,379 - core.staging_automation - INFO - Processing record 1/1: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060
2025-06-10 14:48:17,380 - core.staging_automation - INFO - Processing record: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060 - 2025-06-10
2025-06-10 14:48:17,380 - core.staging_automation - INFO - Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-10 14:48:24,293 - core.staging_automation - ERROR - Failed to fill date field: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,293 - core.staging_automation - ERROR - Failed to fill form for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,294 - core.staging_automation - ERROR - Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb42c01]
	(No symbol) [0x0xb41ce0]
	(No symbol) [0x0xb383d2]
	(No symbol) [0x0xb368d1]
	(No symbol) [0x0xb39c4a]
	(No symbol) [0x0xb39cc7]
	(No symbol) [0x0xb7a51a]
	(No symbol) [0x0xb7a5a1]
	(No symbol) [0x0xb71fd4]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xb6eed4]
	(No symbol) [0x0xb9e7f4]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:48:24,294 - core.staging_automation - INFO - Completed processing 1 records
2025-06-10 14:48:24,295 - automation_service - INFO - Job auto_20250610_144655 completed: 0 successful, 1 failed
2025-06-10 14:48:26,574 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-10 14:48:26,575 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-10 14:52:47,181 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:47] "GET / HTTP/1.1" 200 -
2025-06-10 14:52:47,365 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:52:47,366 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-10 14:52:52,462 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:52:52,477 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:52] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:52:52,633 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-10 14:52:52,635 - werkzeug - INFO - ********** - - [10/Jun/2025 14:52:52] "GET /api/employees HTTP/1.1" 200 -
2025-06-10 14:53:03,016 - data_interface.app - INFO - Processing 1 selected records
2025-06-10 14:53:03,017 - automation_service - INFO - Starting automation job auto_20250610_145303
2025-06-10 14:53:03,020 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-10 14:53:03,021 - WDM - INFO - ====== WebDriver manager ======
2025-06-10 14:53:03,018 - automation_service - INFO - Started automation job auto_20250610_145303 for 1 records
2025-06-10 14:53:03,027 - werkzeug - INFO - ********** - - [10/Jun/2025 14:53:03] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-10 14:53:04,024 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:53:04,313 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-10 14:53:04,476 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-10 14:53:05,055 - data_interface.app - INFO - Using cached staging data
2025-06-10 14:53:05,057 - werkzeug - INFO - ********** - - [10/Jun/2025 14:53:05] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-10 14:53:05,996 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-10 14:53:06,007 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-10 14:53:06,008 - automation_service - INFO - Created 1 mock staging records
2025-06-10 14:53:06,008 - automation_service - INFO - Processing 1 staging records
2025-06-10 14:53:26,113 - core.staging_automation - INFO - Performing login...
2025-06-10 14:53:26,114 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:53:34,324 - core.staging_automation - ERROR - Login failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:34,326 - core.staging_automation - ERROR - Login check failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:34,327 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-10 14:53:42,582 - core.staging_automation - ERROR - Login failed: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:42,584 - core.staging_automation - ERROR - Error processing staging records: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=137.0.7151.69)
Stacktrace:
	GetHandleVerifier [0x0xd03763+63299]
	GetHandleVerifier [0x0xd037a4+63364]
	(No symbol) [0x0xb31113]
	(No symbol) [0x0xb2e64e]
	(No symbol) [0x0xb21e42]
	(No symbol) [0x0xb23878]
	(No symbol) [0x0xb220d8]
	(No symbol) [0x0xb21c13]
	(No symbol) [0x0xb21921]
	(No symbol) [0x0xb1f8c4]
	(No symbol) [0x0xb2026b]
	(No symbol) [0x0xb34b3e]
	(No symbol) [0x0xbc0657]
	(No symbol) [0x0xb9e57c]
	(No symbol) [0x0xbbfa4a]
	(No symbol) [0x0xb9e376]
	(No symbol) [0x0xb6d6e0]
	(No symbol) [0x0xb6e544]
	GetHandleVerifier [0x0xf5e033+2531347]
	GetHandleVerifier [0x0xf59332+2511634]
	GetHandleVerifier [0x0xd29eda+220858]
	GetHandleVerifier [0x0xd1a528+156936]
	GetHandleVerifier [0x0xd20c5d+183357]
	GetHandleVerifier [0x0xd0b6c8+95912]
	GetHandleVerifier [0x0xd0b870+96336]
	GetHandleVerifier [0x0xcf664a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-10 14:53:42,585 - automation_service - INFO - Job auto_20250610_145303 completed: 0 successful, 1 failed
2025-06-10 14:53:44,852 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-10 14:53:44,853 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-10 15:57:15,102 - __main__ - INFO - System shutdown complete
2025-06-11 07:38:43,878 - __main__ - INFO - Configuration loaded successfully
2025-06-11 07:38:45,903 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-06-11 07:38:45,903 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 07:39:15,532 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:15] "GET / HTTP/1.1" 200 -
2025-06-11 07:39:16,041 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:39:16,043 - data_interface.app - INFO - Fetching staging data from http://localhost:5173/api/staging/data
2025-06-11 07:39:16,666 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:16] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 07:39:21,078 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:39:21,080 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:21] "GET /api/employees HTTP/1.1" 200 -
2025-06-11 07:39:21,346 - data_interface.app - INFO - Successfully fetched 31 staging records
2025-06-11 07:39:21,347 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:21] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 07:39:25,862 - data_interface.app - INFO - Processing 1 selected records
2025-06-11 07:39:25,863 - automation_service - INFO - Starting automation job auto_20250611_073925
2025-06-11 07:39:25,863 - automation_service - INFO - Started automation job auto_20250611_073925 for 1 records
2025-06-11 07:39:25,865 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:25] "POST /api/process-selected HTTP/1.1" 200 -
2025-06-11 07:39:25,867 - core.staging_automation - INFO - Initializing Staging Automation Engine
2025-06-11 07:39:25,868 - WDM - INFO - ====== WebDriver manager ======
2025-06-11 07:39:27,498 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:27,844 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:28,009 - WDM - INFO - There is no [win64] chromedriver "137.0.7151.70" for browser google-chrome "137.0.7151" in cache
2025-06-11 07:39:28,010 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:28,199 - data_interface.app - INFO - Using cached staging data
2025-06-11 07:39:28,201 - werkzeug - INFO - ********** - - [11/Jun/2025 07:39:28] "GET /api/staging-data?status=staged HTTP/1.1" 200 -
2025-06-11 07:39:28,486 - WDM - INFO - WebDriver version 137.0.7151.70 selected
2025-06-11 07:39:28,491 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 07:39:28,492 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.70/win32/chromedriver-win32.zip
2025-06-11 07:39:28,710 - WDM - INFO - Driver downloading response is 200
2025-06-11 07:39:37,094 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-11 07:39:38,096 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.70]
2025-06-11 07:39:40,292 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-11 07:39:40,297 - core.staging_automation - INFO - Staging Automation Engine initialized successfully
2025-06-11 07:39:40,297 - automation_service - INFO - Created 1 mock staging records
2025-06-11 07:39:40,298 - automation_service - INFO - Processing 1 staging records
2025-06-11 07:40:00,368 - core.staging_automation - INFO - Performing login...
2025-06-11 07:40:00,368 - core.staging_automation - INFO - Navigating to login page: http://millwarep3:8004/
2025-06-11 07:40:18,432 - core.staging_automation - INFO - Dismissed popup using selector: #MainContent_btnOkay
2025-06-11 07:40:18,432 - core.staging_automation - INFO - Login completed successfully
2025-06-11 07:40:18,433 - core.staging_automation - INFO - Processing record 1/1: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060
2025-06-11 07:40:18,433 - core.staging_automation - INFO - Processing record: Employee c1e595b4-d498-4320-bce3-8d0f0cf52060 - 2025-06-10
2025-06-11 07:40:18,442 - core.staging_automation - INFO - Navigating to task register: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-11 07:40:40,427 - core.staging_automation - ERROR - Failed to fill date field: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,428 - core.staging_automation - ERROR - Failed to fill form for record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,429 - core.staging_automation - ERROR - Failed to process record c1e595b4-d498-4320-bce3-8d0f0cf52060: Message: stale element reference: stale element not found
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0xd73783+63299]
	GetHandleVerifier [0x0xd737c4+63364]
	(No symbol) [0x0xba1113]
	(No symbol) [0x0xbb2c01]
	(No symbol) [0x0xbb1ce0]
	(No symbol) [0x0xba83d2]
	(No symbol) [0x0xba68d1]
	(No symbol) [0x0xba9c4a]
	(No symbol) [0x0xba9cc7]
	(No symbol) [0x0xbea51a]
	(No symbol) [0x0xbea5a1]
	(No symbol) [0x0xbe1fd4]
	(No symbol) [0x0xc0e57c]
	(No symbol) [0x0xbdeed4]
	(No symbol) [0x0xc0e7f4]
	(No symbol) [0x0xc2fa4a]
	(No symbol) [0x0xc0e376]
	(No symbol) [0x0xbdd6e0]
	(No symbol) [0x0xbde544]
	GetHandleVerifier [0x0xfce073+2531379]
	GetHandleVerifier [0x0xfc9372+2511666]
	GetHandleVerifier [0x0xd99efa+220858]
	GetHandleVerifier [0x0xd8a548+156936]
	GetHandleVerifier [0x0xd90c7d+183357]
	GetHandleVerifier [0x0xd7b6e8+95912]
	GetHandleVerifier [0x0xd7b890+96336]
	GetHandleVerifier [0x0xd6666a+9770]
	BaseThreadInitThunk [0x0x76115d49+25]
	RtlInitializeExceptionChain [0x0x7711d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7711cfc1+561]

2025-06-11 07:40:40,429 - core.staging_automation - INFO - Completed processing 1 records
2025-06-11 07:40:40,429 - automation_service - INFO - Job auto_20250611_073925 completed: 0 successful, 1 failed
2025-06-11 07:40:42,602 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-11 07:40:42,603 - core.staging_automation - INFO - Staging automation engine cleaned up
2025-06-11 07:42:52,278 - __main__ - INFO - System shutdown complete
