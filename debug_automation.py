#!/usr/bin/env python3
"""
Venus AutoFill Debug Script
Diagnose automation issues and test flow execution
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# Add src to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Now import the main application
try:
    from main import SeleniumAutoFillApp
    print("✅ Successfully imported SeleniumAutoFillApp")
except ImportError as e:
    print(f"❌ Failed to import SeleniumAutoFillApp: {e}")
    sys.exit(1)

class AutomationDebugger:
    def __init__(self):
        self.app = SeleniumAutoFillApp()
        self.setup_debug_logging()
    
    def setup_debug_logging(self):
        """Setup detailed debug logging"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('debug_automation.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
    async def diagnose_startup(self):
        """Diagnose application startup issues"""
        print("🔍 DIAGNOSING APPLICATION STARTUP")
        print("=" * 50)
        
        try:
            # Test configuration loading
            print("1. Testing configuration loading...")
            config = self.app.load_configuration()
            print(f"   ✅ Config loaded: {len(config)} sections")
            print(f"   📍 Login URL: {config['urls']['login']}")
            
            # Test browser initialization
            print("2. Testing browser initialization...")
            await self.app.initialize()
            print(f"   ✅ Browser initialized: {self.app.driver.current_url}")
            
            # Test flow file loading
            print("3. Testing flow file loading...")
            flows_dir = Path("flows")
            if flows_dir.exists():
                pre_login_file = flows_dir / "pre_login_flow.json"
                if pre_login_file.exists():
                    flow_data = self.app.load_flow_from_file(str(pre_login_file))
                    print(f"   ✅ Pre-login flow loaded: {len(flow_data)} events")
                    
                    # Show first few events
                    for i, event in enumerate(flow_data[:3]):
                        print(f"      Event {i+1}: {event.get('type')} - {event.get('description', 'No description')}")
                else:
                    print("   ❌ Pre-login flow file not found")
            else:
                print("   ❌ Flows directory not found")
                
            return True
            
        except Exception as e:
            print(f"   ❌ Startup error: {e}")
            logging.exception("Startup diagnostic failed")
            return False
    
    async def test_basic_navigation(self):
        """Test basic navigation to target URL"""
        print("\n🧭 TESTING BASIC NAVIGATION")
        print("=" * 50)
        
        try:
            target_url = self.app.config['urls']['login']
            print(f"1. Navigating to: {target_url}")
            
            # Navigate to URL
            self.app.driver.get(target_url)
            await self.app._wait_for_page_stability()
            
            current_url = self.app.driver.current_url
            page_title = self.app.driver.title
            
            print(f"   ✅ Current URL: {current_url}")
            print(f"   ✅ Page Title: {page_title}")
            
            # Test element detection
            print("2. Testing login form detection...")
            try:
                username_field = self.app.driver.find_element("id", "txtUsername")
                password_field = self.app.driver.find_element("id", "txtPassword")
                login_button = self.app.driver.find_element("id", "btnLogin")
                
                print("   ✅ Username field found")
                print("   ✅ Password field found") 
                print("   ✅ Login button found")
                return True
                
            except Exception as e:
                print(f"   ❌ Login form elements not found: {e}")
                
                # Show available elements for debugging
                try:
                    body_text = self.app.driver.find_element("tag name", "body").text[:200]
                    print(f"   📄 Page content preview: {body_text}...")
                except:
                    print("   📄 Could not get page content")
                return False
                
        except Exception as e:
            print(f"   ❌ Navigation error: {e}")
            logging.exception("Navigation test failed")
            return False
    
    async def test_flow_execution(self):
        """Test minimal flow execution"""
        print("\n⚙️ TESTING FLOW EXECUTION")
        print("=" * 50)
        
        try:
            # Create a minimal test flow
            test_flow = [
                {
                    "id": 1,
                    "type": "navigate",
                    "url": self.app.config['urls']['login'],
                    "timeout": 5000,
                    "description": "Navigate to login page"
                },
                {
                    "id": 2,
                    "type": "wait",
                    "duration": 2000,
                    "description": "Wait for page to load"
                }
            ]
            
            print("1. Executing minimal test flow...")
            print(f"   📋 Flow events: {len(test_flow)}")
            
            # Execute the test flow
            result = await self.app.automation_engine.execute_automation_flow(test_flow)
            
            if result.success:
                print("   ✅ Flow execution successful")
                print(f"   📊 Events executed: {result.events_executed}")
                return True
            else:
                print("   ❌ Flow execution failed")
                print(f"   🔥 Errors: {result.errors}")
                return False
                
        except Exception as e:
            print(f"   ❌ Flow execution error: {e}")
            logging.exception("Flow execution test failed")
            return False
    
    async def test_element_finding(self):
        """Test element finding capabilities"""
        print("\n🔍 TESTING ELEMENT FINDING")
        print("=" * 50)
        
        try:
            # Navigate to login page first
            self.app.driver.get(self.app.config['urls']['login'])
            await self.app._wait_for_page_stability()
            
            # Test different selectors
            selectors_to_test = [
                {"selector": "#txtUsername", "description": "Username field by ID"},
                {"selector": "input[name='txtUsername']", "description": "Username field by name"},
                {"selector": "#txtPassword", "description": "Password field by ID"},
                {"selector": "#btnLogin", "description": "Login button by ID"}
            ]
            
            for test in selectors_to_test:
                try:
                    element = await self.app.automation_engine.element_finder.find_element_with_multiple_methods(test["selector"])
                    if element:
                        print(f"   ✅ {test['description']}: Found")
                    else:
                        print(f"   ❌ {test['description']}: Not found")
                except Exception as e:
                    print(f"   ❌ {test['description']}: Error - {e}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Element finding test error: {e}")
            logging.exception("Element finding test failed")
            return False
    
    async def full_diagnostic(self):
        """Run complete diagnostic suite"""
        print("🚀 VENUS AUTOFILL AUTOMATION DIAGNOSTIC")
        print("=" * 60)
        
        results = {}
        
        # Run all diagnostic tests
        results['startup'] = await self.diagnose_startup()
        
        if results['startup']:
            results['navigation'] = await self.test_basic_navigation()
            results['element_finding'] = await self.test_element_finding()
            results['flow_execution'] = await self.test_flow_execution()
        
        # Summary
        print("\n📊 DIAGNOSTIC SUMMARY")
        print("=" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name.upper()}: {status}")
        
        all_passed = all(results.values())
        
        if all_passed:
            print("\n🎉 ALL TESTS PASSED - Automation should work correctly!")
            print("\n💡 NEXT STEPS:")
            print("   1. Run python src/main.py")
            print("   2. Select option 1 (AUTO COMPLETE FLOW)")
            print("   3. Monitor the console for any errors")
        else:
            print("\n⚠️ SOME TESTS FAILED - Check the errors above")
            print("\n🔧 TROUBLESHOOTING:")
            print("   1. Check that millwarep3:8004 is accessible")
            print("   2. Verify Chrome browser is installed")
            print("   3. Check network connectivity")
            print("   4. Review debug_automation.log for details")
        
        return all_passed
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            await self.app.cleanup()
        except Exception as e:
            print(f"Cleanup error: {e}")

async def main():
    """Main diagnostic function"""
    debugger = AutomationDebugger()
    
    try:
        await debugger.full_diagnostic()
    except KeyboardInterrupt:
        print("\n\nDiagnostic interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error during diagnostic: {e}")
        logging.exception("Diagnostic failed")
    finally:
        await debugger.cleanup()
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    asyncio.run(main()) 