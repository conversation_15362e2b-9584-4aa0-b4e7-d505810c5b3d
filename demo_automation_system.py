#!/usr/bin/env python3
"""
Demo Script for Automated Data Entry System
Demonstrates how to use the system programmatically
"""

import sys
import time
import requests
import json
from pathlib import Path

def demo_api_usage():
    """Demonstrate API usage"""
    base_url = "http://localhost:5000"
    
    print("🎬 DEMO: Automated Data Entry System API Usage")
    print("=" * 60)
    
    print("\n1. 📊 Fetching staging data...")
    try:
        response = requests.get(f"{base_url}/api/staging-data?limit=5")
        if response.status_code == 200:
            data = response.json()
            records = data.get('data', [])
            print(f"   Found {len(records)} staging records")
            
            if records:
                print("   Sample record:")
                sample = records[0]
                print(f"     Employee: {sample.get('employee_name')}")
                print(f"     Date: {sample.get('date')}")
                print(f"     Task: {sample.get('task_code')}")
                print(f"     Charge Job: {sample.get('raw_charge_job', '')[:50]}...")
        else:
            print(f"   ❌ Failed to fetch data: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed - is the system running?")
        print("   Start with: python run_automation_system.py")
        return False
    
    print("\n2. 👥 Getting employee list...")
    try:
        response = requests.get(f"{base_url}/api/employees")
        if response.status_code == 200:
            employees = response.json()
            print(f"   Found {len(employees)} unique employees")
            if employees:
                print(f"   Examples: {', '.join(employees[:3])}")
        else:
            print(f"   ❌ Failed to get employees: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n3. 🚀 Starting automation job...")
    try:
        # Create sample job with mock record IDs
        test_payload = {
            "selected_ids": [
                "POM00132_2025-06-10",
                "POM00181_2025-06-10"
            ]
        }
        
        response = requests.post(
            f"{base_url}/api/process-selected",
            json=test_payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('automation_id')
            print(f"   ✅ Started automation job: {job_id}")
            print(f"   Processing {result.get('selected_count')} records")
            
            # Monitor job progress
            print("\n4. 📋 Monitoring job progress...")
            for i in range(10):  # Check for up to 10 iterations
                time.sleep(2)
                
                response = requests.get(f"{base_url}/api/job-status/{job_id}")
                if response.status_code == 200:
                    status = response.json()
                    job_status = status.get('status')
                    print(f"   Status: {job_status}")
                    
                    if job_status in ['completed', 'failed']:
                        if job_status == 'completed':
                            results = status.get('results', [])
                            successful = sum(1 for r in results if r.get('success'))
                            print(f"   ✅ Job completed: {successful}/{len(results)} records processed successfully")
                            
                            # Show processing details
                            for result in results:
                                record_id = result.get('record_id')
                                success = result.get('success')
                                processing_time = result.get('processing_time', 0)
                                status_icon = "✅" if success else "❌"
                                print(f"     {status_icon} {record_id}: {processing_time:.1f}s")
                        else:
                            error_msg = status.get('error_message', 'Unknown error')
                            print(f"   ❌ Job failed: {error_msg}")
                        break
                    elif job_status == 'running':
                        print(f"   🔄 Job is running... (check {i+1}/10)")
                else:
                    print(f"   ❌ Failed to get job status: {response.status_code}")
                    break
            else:
                print("   ⏰ Job monitoring timeout")
                
        else:
            print(f"   ❌ Failed to start job: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n5. 📈 Getting all jobs...")
    try:
        response = requests.get(f"{base_url}/api/jobs")
        if response.status_code == 200:
            jobs = response.json()
            print(f"   Found {len(jobs)} total jobs")
            
            for job in jobs[-3:]:  # Show last 3 jobs
                job_id = job.get('job_id')
                status = job.get('status')
                created = job.get('created_at', '')[:19]  # Remove microseconds
                record_count = job.get('selected_records_count', 0)
                print(f"     {job_id}: {status} ({record_count} records) - {created}")
        else:
            print(f"   ❌ Failed to get jobs: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n✅ Demo completed!")
    return True

def demo_staging_data_format():
    """Show the expected staging data format"""
    print("\n📋 STAGING DATA FORMAT")
    print("=" * 30)
    
    sample_staging_data = {
        "data": [
            {
                "id": "POM00132_2025-06-10",
                "employee_name": "SEPTIAN ADE PRATAMA",
                "employee_id": "POM00132",
                "date": "2025-06-10",
                "task_code": "OC7190",
                "station_code": "STN-BLR",
                "raw_charge_job": "(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)",
                "status": "staged",
                "hours": 7.0,
                "unit": 1.0
            },
            {
                "id": "POM00181_2025-06-10",
                "employee_name": "ADE PRASETYA",
                "employee_id": "POM00181",
                "date": "2025-06-10",
                "task_code": "OC7240",
                "station_code": "STN-LAB",
                "raw_charge_job": "(OC7240) LABORATORY ANALYSIS / STN-LAB (STATION LABORATORY) / LAB00000 (LABOUR COST) / L (LABOUR)",
                "status": "staged",
                "hours": 8.0,
                "unit": 1.0
            }
        ],
        "total": 2,
        "page": 1,
        "per_page": 50
    }
    
    print("Your staging data API should return data in this format:")
    print(json.dumps(sample_staging_data, indent=2))
    
    print("\nKey fields:")
    print("  - id: Unique identifier for the record")
    print("  - employee_name: Full name for autocomplete selection")
    print("  - employee_id: Employee ID code")
    print("  - date: Date in YYYY-MM-DD format")
    print("  - raw_charge_job: Complete charge job string with '/' separators")
    print("  - status: Record status (staged, processing, completed, failed)")

def demo_automation_flow():
    """Show the automation flow steps"""
    print("\n🤖 AUTOMATION FLOW")
    print("=" * 20)
    
    print("For each selected staging record, the system will:")
    print("1. 🔐 Login to Millware system (if not already logged in)")
    print("   - Navigate to http://millwarep3:8004/")
    print("   - Enter username: adm075")
    print("   - Enter password: adm075")
    print("   - Handle any login popups")
    
    print("\n2. 📝 Navigate to Task Register page")
    print("   - Go to: http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx")
    
    print("\n3. 📊 Fill form with record data:")
    print("   - Date: Convert YYYY-MM-DD to DD/MM/YYYY format")
    print("   - Employee: Use autocomplete to select employee")
    print("   - Parse raw_charge_job into 4 parts:")
    print("     * Task Code: (OC7190) BOILER OPERATION")
    print("     * Station Code: STN-BLR (STATION BOILER)")
    print("     * Machine Code: BLR00000 (LABOUR COST)")
    print("     * Expense Code: L (LABOUR)")
    
    print("\n4. ✅ Submit form:")
    print("   - Send Arrow Down key")
    print("   - Send Enter key")
    print("   - Wait for page reload (dynamic detection)")
    
    print("\n5. 🔄 Repeat for next record")

def main():
    """Main demo function"""
    print("🎬 AUTOMATED DATA ENTRY SYSTEM - DEMO")
    print("=" * 50)
    
    print("\nThis demo shows how to use the automated data entry system.")
    print("Make sure the system is running before proceeding.")
    print("\nTo start the system:")
    print("  python run_automation_system.py")
    print("\nWeb interface: http://localhost:5000")
    
    choice = input("\nPress Enter to run API demo, 's' to show staging format, 'f' to show flow, or 'q' to quit: ").strip().lower()
    
    if choice == 'q':
        print("Goodbye!")
        return
    elif choice == 's':
        demo_staging_data_format()
    elif choice == 'f':
        demo_automation_flow()
    else:
        # Run API demo
        success = demo_api_usage()
        
        if success:
            print("\n🎉 Demo completed successfully!")
            print("\nNext steps:")
            print("1. Set up your staging data API at http://localhost:5173/api/staging/data")
            print("2. Ensure Millware system is accessible at http://millwarep3:8004/")
            print("3. Use the web interface at http://localhost:5000 to select and process records")
        else:
            print("\n❌ Demo failed. Please check that the system is running.")

if __name__ == "__main__":
    main()
