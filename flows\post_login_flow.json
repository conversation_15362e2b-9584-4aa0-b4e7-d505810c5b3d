{"name": "Millware Task Register Flow", "description": "Complete task register automation using reliable field targeting", "version": "4.2.0", "variables": {"taskRegisterUrl": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx", "transactionDate": "03/06/2025", "employeeCode": "POM00132", "employeeName": "SEPTIAN ADE PRATAMA", "taskCode": "OC7240", "taskName": "(OC7240) LABORATORY ANALYSIS", "stationCode": "STN-LAB", "stationName": "STN-LAB (STATION LABORATORY)", "machineCode": "LAB00000", "machineName": "LAB00000 (LABOUR COST)", "expenseCode": "L", "expenseName": "L (LABOUR)"}, "events": [{"id": 1, "type": "wait_for_element", "selector": "#MainContent_txtTrxDate", "timeout": 3000, "description": "Wait for transaction date field"}, {"id": 2, "type": "input", "selector": "#MainContent_txtTrxDate", "value": "{transactionDate}", "clearFirst": true, "description": "Enter transaction date (03/06/2025)"}, {"id": 3, "type": "click", "selector": "body", "description": "Click elsewhere to unfocus date field and trigger page reload"}, {"id": 4, "type": "wait", "duration": 1500, "description": "Wait for page reload after date input"}, {"id": 5, "type": "wait_for_element", "selector": "#MainContent_ddlEmployee + input.ui-autocomplete-input", "timeout": 5000, "description": "Wait for employee autocomplete field (adjacent to hidden select)"}, {"id": 6, "type": "input", "selector": "#MainContent_ddlEmployee + input.ui-autocomplete-input", "value": "{employeeName}", "clearFirst": true, "description": "Enter employee name: SEPTIAN ADE PRATAMA"}, {"id": 7, "type": "keyboard", "selector": "#MainContent_ddlEmployee + input.ui-autocomplete-input", "key": "ArrowDown", "waitAfterKey": 500, "description": "Navigate to employee suggestion"}, {"id": 8, "type": "keyboard", "selector": "#MainContent_ddlEmployee + input.ui-autocomplete-input", "key": "Enter", "preventDefault": true, "waitAfterKey": 1500, "description": "Select employee and wait for page reload"}, {"id": 9, "type": "wait_for_element", "selector": "#MainContent_ddlTaskCode + input.ui-autocomplete-input", "timeout": 5000, "description": "Wait for Task Code autocomplete field (adjacent to hidden select)"}, {"id": 10, "type": "input", "selector": "#MainContent_ddlTaskCode + input.ui-autocomplete-input", "value": "{taskName}", "clearFirst": true, "description": "Enter task code: (OC7240) LABORATORY ANALYSIS"}, {"id": 11, "type": "keyboard", "selector": "#MainContent_ddlTaskCode + input.ui-autocomplete-input", "key": "ArrowDown", "waitAfterKey": 500, "description": "Navigate to task code suggestion"}, {"id": 12, "type": "keyboard", "selector": "#MainContent_ddlTaskCode + input.ui-autocomplete-input", "key": "Enter", "preventDefault": true, "waitAfterKey": 1500, "description": "Select task code and wait for additional fields to appear"}, {"id": 13, "type": "wait", "duration": 1500, "description": "Wait for page to stabilize and additional autocomplete fields to load"}, {"id": 14, "type": "wait_for_element", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(3)", "timeout": 5000, "description": "Wait for Station Code autocomplete field to appear (3rd field)"}, {"id": 15, "type": "input", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(3)", "value": "{stationName}", "clearFirst": true, "description": "Enter station code: STN-LAB (STATION LABORATORY)"}, {"id": 16, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(3)", "key": "ArrowDown", "waitAfterKey": 500, "description": "Navigate to station code suggestion"}, {"id": 17, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(3)", "key": "Enter", "preventDefault": true, "waitAfterKey": 1000, "description": "Select station code"}, {"id": 18, "type": "wait_for_element", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(4)", "timeout": 3000, "description": "Wait for Machine Code autocomplete field to appear (4th field)"}, {"id": 19, "type": "input", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(4)", "value": "{machineName}", "clearFirst": true, "description": "Enter machine code: LAB00000 (LABOUR COST)"}, {"id": 20, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(4)", "key": "ArrowDown", "waitAfterKey": 500, "description": "Navigate to machine code suggestion"}, {"id": 21, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(4)", "key": "Enter", "preventDefault": true, "waitAfterKey": 1000, "description": "Select machine code"}, {"id": 22, "type": "wait_for_element", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(5)", "timeout": 3000, "description": "Wait for Expense Code autocomplete field to appear (5th field)"}, {"id": 23, "type": "input", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(5)", "value": "{expenseName}", "clearFirst": true, "description": "Enter expense code: L (LABOUR)"}, {"id": 24, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(5)", "key": "ArrowDown", "waitAfterKey": 500, "description": "Navigate to expense code suggestion"}, {"id": 25, "type": "keyboard", "selector": "input.ui-autocomplete-input.CBOBox:nth-of-type(5)", "key": "Enter", "preventDefault": true, "waitAfterKey": 1000, "description": "Select expense code"}, {"id": 26, "type": "wait", "duration": 1000, "description": "Final wait for all fields to be populated and validated"}]}