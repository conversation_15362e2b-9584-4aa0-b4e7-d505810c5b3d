{"name": "Millware Pre-Login Flow", "description": "Fast login sequence for Millware system", "version": "1.1.0", "variables": {"baseUrl": "http://millwarep3:8004/", "username": "adm075", "password": "adm075", "taskRegisterUrl": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"}, "events": [{"id": 1, "type": "navigate", "url": "{baseUrl}", "timeout": 3000, "description": "Navigate to Millware login page"}, {"id": 2, "type": "wait_for_element", "selector": "#txtUsername", "timeout": 3000, "description": "Wait for username field to be available"}, {"id": 3, "type": "input", "selector": "#txtUsername", "value": "{username}", "clearFirst": true, "description": "Enter username in login form"}, {"id": 4, "type": "input", "selector": "#txtPassword", "value": "{password}", "clearFirst": true, "description": "Enter password in login form"}, {"id": 5, "type": "click", "selector": "#btnLogin", "description": "Click login button"}, {"id": 6, "type": "wait", "duration": 1500, "description": "Wait for login to process"}, {"id": 7, "type": "popup_handler", "popupSelectors": ["#MainContent_mpopLocation_backgroundElement", ".ModalPopupBG", "[class*='modal']", "[class*='popup']"], "okButtonSelectors": ["#MainContent_btnOkay", "#btnOkay", "input[type='button'][value*='OK']", "input[type='button'][value*='Ok']", ".button"], "timeout": 5000, "stabilizationDelay": 1000, "dismissalTimeout": 3000, "description": "Handle location setting popup after login"}, {"id": 8, "type": "wait", "duration": 1000, "description": "Wait for popup to be fully dismissed"}, {"id": 9, "type": "navigate", "url": "{taskRegisterUrl}", "timeout": 5000, "description": "Navigate directly to Task Register page"}, {"id": 10, "type": "wait", "duration": 1000, "description": "Wait for task register page to load"}]}