{"name": "Sample Login Flow", "description": "Basic login automation flow", "metadata": {"version": "1.0", "created": "2024-01-01", "author": "Selenium AutoFill"}, "variables": {"username": "adm075", "password": "adm075", "loginUrl": "http://millwarep3.rebinmas.com:8003/login"}, "events": [{"id": 1, "type": "open_to", "url": "{loginUrl}", "waitForLoad": true, "description": "Navigate to login page"}, {"id": 2, "type": "wait_for_element", "selector": "#username", "timeout": 10000, "expectVisible": true, "description": "Wait for username field to be visible"}, {"id": 3, "type": "input", "selector": "#username", "value": "{username}", "clearFirst": true, "simulateTyping": false, "description": "Enter username"}, {"id": 4, "type": "input", "selector": "#password", "value": "{password}", "clearFirst": true, "simulateTyping": false, "description": "Enter password"}, {"id": 5, "type": "click", "selector": "#login-button", "alternatives": [{"type": "css", "selector": "button[type='submit']"}, {"type": "text", "selector": "<PERSON><PERSON>"}, {"type": "xpath", "selector": "//button[contains(text(), 'Login')]"}], "description": "Click login button"}, {"id": 6, "type": "wait_for_page_stability", "timeout": 15000, "description": "Wait for login to complete and page to stabilize"}, {"id": 7, "type": "if_then_else", "condition": {"type": "url_contains", "value": "dashboard"}, "thenEvents": [{"type": "extract", "selector": ".user-name, .username, .current-user", "name": "loggedInUser", "attribute": "text", "storeGlobally": true}], "elseEvents": [{"type": "extract", "selector": ".error-message, .alert-danger, .login-error", "name": "loginError", "attribute": "text", "storeGlobally": true}], "description": "Check login result and extract relevant information"}]}