# Build Scripts: Venus AutoFill Build Process

## Build System Overview

### Build Philosophy
- **Simplicity**: Straightforward build process that works across platforms
- **Automation**: Minimal manual intervention required
- **Reproducibility**: Consistent builds across different environments
- **Flexibility**: Support for different deployment scenarios
- **Documentation**: Clear instructions for build and deployment

### Build Tools
- **Python pip**: Primary package management and dependency resolution
- **setuptools**: Python package building and distribution
- **Virtual Environment**: Isolated Python environment for consistent builds
- **PowerShell/Bash**: Cross-platform build scripts
- **Configuration Templates**: Dynamic configuration generation

## Build Configuration Files

### Setup Configuration (`setup.py`)
```python
"""
Setup configuration for Venus AutoFill Selenium Browser Automation
"""
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="venus-autofill-selenium",
    version="1.0.0",
    author="Venus AutoFill Team",
    author_email="<EMAIL>",
    description="Selenium-based browser automation for Millware ERP system",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/venus-autofill-selenium",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Software Development :: Testing",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-asyncio>=0.21.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "venus-autofill=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.yaml", "*.md"],
        "flows": ["*.json"],
        "config": ["*.json"],
    },
)
```

### Build Configuration (`pyproject.toml`)
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "venus-autofill-selenium"
version = "1.0.0"
description = "Selenium-based browser automation for Millware ERP system"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Venus AutoFill Team", email = "<EMAIL>"},
]
keywords = ["automation", "selenium", "browser", "erp", "millware"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10", 
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "selenium==4.15.2",
    "webdriver-manager==4.0.1",
    "PyQt5==5.15.10",
    "beautifulsoup4==4.12.2",
    "requests==2.31.0",
    "Pillow==10.1.0",
    "jsonschema==4.20.0",
    "pyyaml==6.0.1",
    "colorama==0.4.6",
    "python-dotenv==1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
venus-autofill = "main:main"
```

## Build Scripts

### Windows Build Script (`build/build.ps1`)
```powershell
#!/usr/bin/env powershell
#
# Venus AutoFill Build Script for Windows
# Builds and packages the application for distribution
#

param(
    [string]$Target = "development",
    [switch]$Clean = $false,
    [switch]$Test = $false,
    [switch]$Package = $false
)

Write-Host "Venus AutoFill Build Script" -ForegroundColor Green
Write-Host "Build Target: $Target" -ForegroundColor Yellow

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectRoot = Split-Path -Parent $ScriptDir

# Set working directory
Set-Location $ProjectRoot

# Clean previous builds
if ($Clean) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue  
    Remove-Item -Path "*.egg-info" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "__pycache__" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Clean completed." -ForegroundColor Green
}

# Create virtual environment
Write-Host "Setting up virtual environment..." -ForegroundColor Yellow
if (-not (Test-Path "venv")) {
    python -m venv venv
}

# Activate virtual environment
& "venv\Scripts\Activate.ps1"

# Upgrade pip
Write-Host "Upgrading pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

# Install development dependencies if needed
if ($Target -eq "development" -or $Test) {
    Write-Host "Installing development dependencies..." -ForegroundColor Yellow
    pip install pytest pytest-cov pytest-asyncio black flake8 mypy
}

# Run tests if requested
if ($Test) {
    Write-Host "Running tests..." -ForegroundColor Yellow
    pytest --cov=src --cov-report=html --cov-report=term-missing
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Tests failed. Build aborted." -ForegroundColor Red
        exit 1
    }
    Write-Host "All tests passed." -ForegroundColor Green
}

# Code quality checks
if ($Target -eq "production") {
    Write-Host "Running code quality checks..." -ForegroundColor Yellow
    
    # Format check with black
    black --check src/
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Code formatting issues found. Run 'black src/' to fix." -ForegroundColor Red
        exit 1
    }
    
    # Lint with flake8
    flake8 src/
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Linting issues found. Please fix before building." -ForegroundColor Red
        exit 1
    }
}

# Package application
if ($Package) {
    Write-Host "Packaging application..." -ForegroundColor Yellow
    
    # Build wheel
    python setup.py sdist bdist_wheel
    
    # Create executable with PyInstaller (optional)
    if (Get-Command pyinstaller -ErrorAction SilentlyContinue) {
        Write-Host "Creating executable..." -ForegroundColor Yellow
        pyinstaller --onefile --windowed --name "VenusAutoFill" src/main.py
    }
    
    Write-Host "Packaging completed." -ForegroundColor Green
}

# Generate configuration templates
Write-Host "Generating configuration templates..." -ForegroundColor Yellow
& "$ScriptDir\generate-config.ps1" -Target $Target

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Target: $Target" -ForegroundColor Yellow

# Deactivate virtual environment
deactivate
```

### Linux/macOS Build Script (`build/build.sh`)
```bash
#!/bin/bash
#
# Venus AutoFill Build Script for Linux/macOS
# Builds and packages the application for distribution
#

set -e  # Exit on any error

# Default values
TARGET="development"
CLEAN=false
TEST=false
PACKAGE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --target)
            TARGET="$2"
            shift 2
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --test)
            TEST=true
            shift
            ;;
        --package)
            PACKAGE=true
            shift
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

echo "Venus AutoFill Build Script"
echo "Build Target: $TARGET"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Set working directory
cd "$PROJECT_ROOT"

# Clean previous builds
if [ "$CLEAN" = true ]; then
    echo "Cleaning previous builds..."
    rm -rf build/ dist/ *.egg-info/ __pycache__/
    find . -name "*.pyc" -delete
    find . -name "__pycache__" -type d -exec rm -rf {} +
    echo "Clean completed."
fi

# Create virtual environment
echo "Setting up virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
python -m pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Install development dependencies if needed
if [ "$TARGET" = "development" ] || [ "$TEST" = true ]; then
    echo "Installing development dependencies..."
    pip install pytest pytest-cov pytest-asyncio black flake8 mypy
fi

# Run tests if requested
if [ "$TEST" = true ]; then
    echo "Running tests..."
    pytest --cov=src --cov-report=html --cov-report=term-missing
    echo "All tests passed."
fi

# Code quality checks
if [ "$TARGET" = "production" ]; then
    echo "Running code quality checks..."
    
    # Format check with black
    black --check src/
    
    # Lint with flake8  
    flake8 src/
fi

# Package application
if [ "$PACKAGE" = true ]; then
    echo "Packaging application..."
    
    # Build wheel
    python setup.py sdist bdist_wheel
    
    # Create executable with PyInstaller (optional)
    if command -v pyinstaller &> /dev/null; then
        echo "Creating executable..."
        pyinstaller --onefile --name "VenusAutoFill" src/main.py
    fi
    
    echo "Packaging completed."
fi

# Generate configuration templates
echo "Generating configuration templates..."
bash "$SCRIPT_DIR/generate-config.sh" --target "$TARGET"

echo "Build completed successfully!"
echo "Target: $TARGET"

# Deactivate virtual environment
deactivate
```

### Configuration Generation Script (`build/generate-config.ps1`)
```powershell
#!/usr/bin/env powershell
#
# Configuration Template Generation Script
#

param(
    [string]$Target = "development"
)

$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Definition)
$ConfigDir = Join-Path $ProjectRoot "config"

Write-Host "Generating configuration templates for target: $Target" -ForegroundColor Yellow

# Ensure config directory exists
if (-not (Test-Path $ConfigDir)) {
    New-Item -ItemType Directory -Path $ConfigDir -Force
}

# Generate app configuration template
$AppConfig = @{
    browser = @{
        headless = $false
        window_size = @(1280, 720)
        disable_notifications = $true
        event_delay = 0.5
    }
    automation = @{
        implicit_wait = 10
        page_load_timeout = 30
        script_timeout = 30
        max_retries = 3
    }
    credentials = @{
        username = "your_username"
        password = "your_password"
    }
    urls = @{
        login = "http://millwarep3.rebinmas.com:8003/"
        attendance = "http://millwarep3.rebinmas.com:8003/attendance"
        taskRegister = "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
    }
}

# Adjust configuration for target environment
switch ($Target) {
    "production" {
        $AppConfig.browser.headless = $true
        $AppConfig.automation.max_retries = 5
    }
    "testing" {
        $AppConfig.urls.login = "http://millwarep3-test.rebinmas.com:8003/"
        $AppConfig.urls.attendance = "http://millwarep3-test.rebinmas.com:8003/attendance"
        $AppConfig.urls.taskRegister = "http://millwarep3-test.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
    }
}

# Write configuration file
$ConfigPath = Join-Path $ConfigDir "app_config_template.json"
$AppConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $ConfigPath -Encoding utf8

Write-Host "Configuration template generated: $ConfigPath" -ForegroundColor Green

# Generate sample form data
$FormData = @{
    sample_task = @{
        task_name = "Sample Task"
        description = "This is a sample task for testing"
        priority = "Medium"
        due_date = "2025-12-31"
    }
}

$FormDataPath = Join-Path $ConfigDir "form_data_template.json"
$FormData | ConvertTo-Json -Depth 10 | Out-File -FilePath $FormDataPath -Encoding utf8

Write-Host "Form data template generated: $FormDataPath" -ForegroundColor Green
```

## Build Commands Reference

### Development Build
```bash
# Windows
.\build\build.ps1 -Target development -Test

# Linux/macOS  
./build/build.sh --target development --test
```

### Production Build
```bash
# Windows
.\build\build.ps1 -Target production -Clean -Test -Package

# Linux/macOS
./build/build.sh --target production --clean --test --package
```

### Quick Build (No Tests)
```bash
# Windows
.\build\build.ps1 -Target development

# Linux/macOS
./build/build.sh --target development
```

### Clean Build
```bash
# Windows
.\build\build.ps1 -Clean

# Linux/macOS
./build/build.sh --clean
```

## Deployment Preparation

### Package Creation
The build script creates several distribution formats:
- **Source Distribution**: `.tar.gz` file for pip installation
- **Wheel Distribution**: `.whl` file for fast installation
- **Executable**: Standalone executable file (if PyInstaller available)
- **Configuration Templates**: Ready-to-use configuration files

### Installation Package Structure
```
dist/
├── venus-autofill-selenium-1.0.0.tar.gz     # Source distribution
├── venus_autofill_selenium-1.0.0-py3-none-any.whl  # Wheel distribution
└── VenusAutoFill.exe                         # Standalone executable (Windows)
```

### Environment-Specific Builds
- **Development**: Full debugging, visible browser, comprehensive logging
- **Testing**: Test environment URLs, enhanced error reporting
- **Production**: Optimized performance, headless browser, minimal logging

## Build Automation

### Continuous Integration Integration
```yaml
# .github/workflows/build.yml
name: Build and Package
on:
  push:
    tags: ['v*']
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
          
      - name: Run build script (Linux/macOS)
        if: runner.os != 'Windows'
        run: ./build/build.sh --target production --clean --test --package
        
      - name: Run build script (Windows)
        if: runner.os == 'Windows'  
        run: .\build\build.ps1 -Target production -Clean -Test -Package
        
      - name: Upload artifacts
        uses: actions/upload-artifact@v2
        with:
          name: dist-${{ matrix.os }}
          path: dist/
```

---

*Created: Monday, June 09, 2025, 09:42 AM WIB*
*Status: Build System Design Complete - Implementation Pending*
*Priority: Implement build scripts and test automated build process* 