2025-06-03 15:38:47,829 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:38:49,258 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:38:49,832 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:38:50,243 - WDM - INFO - There is no [win64] chromedriver "136.0.7103.113" for browser google-chrome "136.0.7103" in cache
2025-06-03 15:38:50,243 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:38:51,089 - WDM - INFO - WebDriver version 136.0.7103.113 selected
2025-06-03 15:38:51,093 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-03 15:38:51,096 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-03 15:38:51,420 - WDM - INFO - Driver downloading response is 200
2025-06-03 15:38:52,939 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:38:54,041 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113]
2025-06-03 15:38:57,906 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:39:07,451 - core.automation_engine - INFO - Executing event 1/6: open_to
2025-06-03 15:39:07,452 - core.automation_engine - INFO - Navigating to: http://your-server.com/login
2025-06-03 15:39:09,963 - core.automation_engine - INFO - Executing event 2/6: wait_for_element
2025-06-03 15:39:20,024 - core.automation_engine - INFO - Waiting for element: #username (visible: True)
2025-06-03 15:39:21,527 - core.automation_engine - ERROR - Error executing event 1: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: unable to send message to renderer
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007D03A0]
	(No symbol) [0x007D0326]
	(No symbol) [0x007CEC93]
	(No symbol) [0x007CF93D]
	(No symbol) [0x007E5147]
	(No symbol) [0x007E58DB]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x00828234]
	(No symbol) [0x00828D3B]
	(No symbol) [0x00870E12]
	(No symbol) [0x0084D2E4]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 15:39:21,528 - core.automation_engine - INFO - Executing event 3/6: input
2025-06-03 15:39:21,627 - core.automation_engine - ERROR - Error executing event 2: Input element not found: #username
2025-06-03 15:39:21,628 - core.automation_engine - INFO - Executing event 4/6: input
2025-06-03 15:39:21,631 - core.automation_engine - ERROR - Error executing event 3: Input element not found: #password
2025-06-03 15:39:21,632 - core.automation_engine - INFO - Executing event 5/6: click
2025-06-03 15:39:21,635 - core.automation_engine - ERROR - Error executing event 4: Element not found: #login-button
2025-06-03 15:39:21,635 - core.automation_engine - INFO - Executing event 6/6: wait_for_page_stability
2025-06-03 15:39:34,210 - main - ERROR - Error in interactive mode: 
2025-06-03 15:39:38,278 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21D2D1D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:39:42,353 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21D2DD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:39:46,409 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21B8BA80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:39:54,574 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21D068D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:39:58,625 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21C1AF10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:40:02,664 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001BA21C1B240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/545f65f4aab1f35e6d4831622b284f59/execute/sync
2025-06-03 15:42:21,993 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:42:22,818 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:42:23,139 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:42:23,483 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 15:42:24,934 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:42:29,026 - main - ERROR - Error in interactive mode: 
2025-06-03 15:46:50,443 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:46:51,465 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:46:51,805 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:46:52,228 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 15:46:53,684 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:47:13,764 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020D63B31810>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cedf1860501b4fb50e4186e27722cd26/elements
2025-06-03 15:47:17,821 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020D63B32350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cedf1860501b4fb50e4186e27722cd26/elements
2025-06-03 15:47:21,856 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020D639FF820>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/cedf1860501b4fb50e4186e27722cd26/elements
2025-06-03 15:47:28,953 - __main__ - ERROR - Error in interactive mode: 
2025-06-03 15:53:51,939 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 15:53:51,939 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:53:52,983 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:53:53,324 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:53:53,659 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 15:53:55,171 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:53:55,190 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:53:58,322 - __main__ - INFO - Application initialized successfully
2025-06-03 15:53:58,324 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 15:53:58,327 - __main__ - INFO - Loaded flow with 12 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 15:53:58,330 - __main__ - INFO - Loaded flow with 22 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 15:53:58,331 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 15:53:58,332 - core.automation_engine - INFO - Executing event 1/35: navigate
2025-06-03 15:53:58,333 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:54:00,189 - core.automation_engine - INFO - Executing event 2/35: wait_for_element
2025-06-03 15:54:00,531 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 15:54:00,959 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 15:54:01,460 - core.automation_engine - INFO - Executing event 3/35: input
2025-06-03 15:54:02,275 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 15:54:02,777 - core.automation_engine - INFO - Executing event 4/35: input
2025-06-03 15:54:03,618 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 15:54:04,119 - core.automation_engine - INFO - Executing event 5/35: click
2025-06-03 15:54:08,784 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 15:54:09,286 - core.automation_engine - INFO - Executing event 6/35: wait
2025-06-03 15:54:09,286 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:54:12,788 - core.automation_engine - INFO - Executing event 7/35: popup_handler
2025-06-03 15:54:12,788 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 15:54:12,803 - core.automation_engine - WARNING - Error handling popup: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x00828234]
	(No symbol) [0x00828D3B]
	(No symbol) [0x00870E12]
	(No symbol) [0x0084D2E4]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 15:54:13,304 - core.automation_engine - INFO - Executing event 8/35: wait_for_element
2025-06-03 15:54:13,644 - core.automation_engine - INFO - Waiting for element: a.popout.level1.static (visible: True)
2025-06-03 15:54:13,982 - core.automation_engine - INFO - Element condition met: a.popout.level1.static
2025-06-03 15:54:14,483 - core.automation_engine - INFO - Executing event 9/35: click
2025-06-03 15:54:16,264 - core.automation_engine - ERROR - Error executing event 8: Message: element click intercepted: Element <div id="selenium-step" class="selenium-step-indicator selenium-highlight-click" data-selenium-highlighted="true">...</div> is not clickable at point (626, 618). Other element would receive the click: <div id="MainContent_mpopLocation_backgroundElement" class="ModalPopupBG" style="position: fixed; left: 0px; top: 0px; z-index: 10000; width: 1252px; height: 694px;"></div>
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x0082ECB0]
	(No symbol) [0x0082D054]
	(No symbol) [0x0082ABF7]
	(No symbol) [0x00829EFB]
	(No symbol) [0x0081E5A5]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 15:54:16,265 - core.automation_engine - INFO - Executing event 10/35: wait
2025-06-03 15:54:16,266 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 15:54:18,767 - core.automation_engine - INFO - Executing event 11/35: navigate
2025-06-03 15:54:18,768 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 15:54:21,924 - core.automation_engine - INFO - Executing event 12/35: wait_for_element
2025-06-03 15:54:31,941 - core.automation_engine - INFO - Waiting for element: #txtEmployee (visible: True)
2025-06-03 15:54:41,959 - core.automation_engine - ERROR - Error executing event 11: Element condition not met within 10.0s: #txtEmployee
2025-06-03 15:54:41,959 - core.automation_engine - INFO - Executing event 13/35: wait
2025-06-03 15:54:41,960 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:54:45,461 - core.automation_engine - INFO - Executing event 14/35: navigate
2025-06-03 15:54:45,462 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 15:54:47,932 - core.automation_engine - INFO - Executing event 15/35: wait_for_element
2025-06-03 15:54:57,055 - core.automation_engine - INFO - Waiting for element: #txtEmployee (visible: True)
2025-06-03 15:55:01,102 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83DC9090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/element
2025-06-03 15:55:05,153 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83DCA0D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/element
2025-06-03 15:55:09,200 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83C93820>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/element
2025-06-03 15:55:13,240 - core.automation_engine - ERROR - Error executing event 14: HTTPConnectionPool(host='localhost', port=62307): Max retries exceeded with url: /session/d850aaa63febee649ac08f304a621178/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83C93950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 15:55:13,242 - core.automation_engine - INFO - Executing event 16/35: prevent_redirect
2025-06-03 15:55:13,242 - core.automation_engine - INFO - Setting up redirect prevention for 30.0 seconds
2025-06-03 15:55:17,303 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B861585F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:21,385 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83CA7AC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:25,439 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B83CA7CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:29,483 - core.automation_engine - WARNING - Failed to set up redirect prevention: HTTPConnectionPool(host='localhost', port=62307): Max retries exceeded with url: /session/d850aaa63febee649ac08f304a621178/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B86147D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 15:55:29,984 - core.automation_engine - INFO - Executing event 17/35: input
2025-06-03 15:55:34,029 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B86147250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:38,084 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B8615C500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:42,133 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B8615C6E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/execute/sync
2025-06-03 15:55:50,228 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B8615C9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/element
2025-06-03 15:55:54,270 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000013B8615CD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d850aaa63febee649ac08f304a621178/element
2025-06-03 15:55:58,319 - __main__ - INFO - Cleaning up resources
2025-06-03 15:57:19,533 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 15:57:19,533 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:57:20,536 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:57:21,566 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:57:22,454 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 15:57:23,857 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:57:23,868 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:57:26,936 - __main__ - INFO - Application initialized successfully
2025-06-03 15:57:26,939 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 15:57:26,940 - __main__ - INFO - Loaded flow with 11 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 15:57:26,941 - __main__ - INFO - Loaded flow with 17 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 15:57:26,942 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 15:57:26,943 - core.automation_engine - INFO - Executing event 1/29: navigate
2025-06-03 15:57:26,944 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:57:28,839 - core.automation_engine - INFO - Executing event 2/29: wait_for_element
2025-06-03 15:57:29,202 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 15:57:29,537 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 15:57:30,038 - core.automation_engine - INFO - Executing event 3/29: input
2025-06-03 15:57:30,767 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 15:57:31,268 - core.automation_engine - INFO - Executing event 4/29: input
2025-06-03 15:57:32,015 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 15:57:32,516 - core.automation_engine - INFO - Executing event 5/29: click
2025-06-03 15:57:36,420 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 15:57:36,921 - core.automation_engine - INFO - Executing event 6/29: wait
2025-06-03 15:57:36,921 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:57:40,423 - core.automation_engine - INFO - Executing event 7/29: popup_handler
2025-06-03 15:57:40,424 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 15:57:40,451 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 15:57:41,797 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 15:57:46,286 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 15:57:47,287 - core.automation_engine - INFO - Executing event 8/29: wait
2025-06-03 15:57:47,288 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:57:50,789 - core.automation_engine - INFO - Executing event 9/29: navigate
2025-06-03 15:57:50,790 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 15:57:53,215 - core.automation_engine - INFO - Executing event 10/29: wait
2025-06-03 15:57:53,216 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 15:57:58,717 - core.automation_engine - INFO - Executing event 11/29: wait_for_element
2025-06-03 15:58:09,304 - core.automation_engine - INFO - Waiting for element: input[id*='txtEmployee'], input[name*='employee'], input[placeholder*='employee'], #txtEmployee (visible: True)
2025-06-03 15:58:13,365 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FEDDD891D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dd59095a19e960afbb77354368273eed/element
2025-06-03 15:58:15,394 - __main__ - INFO - Cleaning up resources
2025-06-03 15:58:19,433 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FEDDC52C40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/dd59095a19e960afbb77354368273eed/execute/sync
2025-06-03 15:58:25,988 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 15:58:25,990 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 15:58:26,802 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:58:27,103 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 15:58:27,399 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 15:58:28,827 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 15:58:28,867 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:58:31,116 - main - INFO - Application initialized successfully
2025-06-03 15:58:31,118 - main - INFO - Starting complete Millware automation flow
2025-06-03 15:58:31,119 - main - INFO - Loaded flow with 11 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 15:58:31,121 - main - INFO - Loaded flow with 17 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 15:58:31,121 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 15:58:31,122 - core.automation_engine - INFO - Executing event 1/29: navigate
2025-06-03 15:58:31,123 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 15:58:32,947 - core.automation_engine - INFO - Executing event 2/29: wait_for_element
2025-06-03 15:58:33,303 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 15:58:33,634 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 15:58:34,134 - core.automation_engine - INFO - Executing event 3/29: input
2025-06-03 15:58:34,829 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 15:58:35,330 - core.automation_engine - INFO - Executing event 4/29: input
2025-06-03 15:58:36,022 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 15:58:36,523 - core.automation_engine - INFO - Executing event 5/29: click
2025-06-03 15:58:40,153 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 15:58:40,654 - core.automation_engine - INFO - Executing event 6/29: wait
2025-06-03 15:58:40,654 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:58:44,156 - core.automation_engine - INFO - Executing event 7/29: popup_handler
2025-06-03 15:58:44,156 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 15:58:44,173 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 15:58:45,514 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 15:58:49,320 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 15:58:50,322 - core.automation_engine - INFO - Executing event 8/29: wait
2025-06-03 15:58:50,322 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:58:53,824 - core.automation_engine - INFO - Executing event 9/29: navigate
2025-06-03 15:58:53,825 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 15:58:56,097 - core.automation_engine - INFO - Executing event 10/29: wait
2025-06-03 15:58:56,098 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 15:59:01,599 - core.automation_engine - INFO - Executing event 11/29: wait_for_element
2025-06-03 15:59:11,648 - core.automation_engine - INFO - Waiting for element: input[id*='txtEmployee'], input[name*='employee'], input[placeholder*='employee'], #txtEmployee (visible: True)
2025-06-03 15:59:32,213 - core.automation_engine - ERROR - Error executing event 10: Element condition not met within 15.0s: input[id*='txtEmployee'], input[name*='employee'], input[placeholder*='employee'], #txtEmployee
2025-06-03 15:59:32,213 - core.automation_engine - INFO - Executing event 12/29: wait
2025-06-03 15:59:32,213 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 15:59:35,715 - core.automation_engine - INFO - Executing event 13/29: navigate
2025-06-03 15:59:35,716 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 15:59:37,910 - core.automation_engine - INFO - Executing event 14/29: wait
2025-06-03 15:59:37,911 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 15:59:43,370 - main - INFO - Cleaning up resources
2025-06-03 16:00:16,634 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:00:16,635 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:00:17,405 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:00:19,007 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:00:19,610 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:00:21,048 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:00:21,064 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:00:23,791 - main - INFO - Application initialized successfully
2025-06-03 16:00:23,792 - main - INFO - Starting complete Millware automation flow
2025-06-03 16:00:23,792 - main - INFO - Loaded flow with 11 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:00:23,793 - main - INFO - Loaded flow with 14 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:00:23,793 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:00:23,793 - core.automation_engine - INFO - Executing event 1/26: navigate
2025-06-03 16:00:23,793 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:00:25,625 - core.automation_engine - INFO - Executing event 2/26: wait_for_element
2025-06-03 16:00:25,971 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:00:26,311 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:00:26,812 - core.automation_engine - INFO - Executing event 3/26: input
2025-06-03 16:00:27,549 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:00:28,051 - core.automation_engine - INFO - Executing event 4/26: input
2025-06-03 16:00:28,756 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:00:29,257 - core.automation_engine - INFO - Executing event 5/26: click
2025-06-03 16:00:33,484 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:00:33,985 - core.automation_engine - INFO - Executing event 6/26: wait
2025-06-03 16:00:33,986 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:00:37,487 - core.automation_engine - INFO - Executing event 7/26: popup_handler
2025-06-03 16:00:37,489 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:00:37,526 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:00:38,858 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:00:44,066 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:00:45,067 - core.automation_engine - INFO - Executing event 8/26: wait
2025-06-03 16:00:45,068 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:00:48,570 - core.automation_engine - INFO - Executing event 9/26: navigate
2025-06-03 16:00:48,570 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:00:50,929 - core.automation_engine - INFO - Executing event 10/26: wait
2025-06-03 16:00:50,929 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 16:00:56,430 - core.automation_engine - INFO - Executing event 11/26: wait_for_element
2025-06-03 16:01:06,490 - core.automation_engine - INFO - Waiting for element: input[id*='txtEmployee'], input[name*='employee'], input[placeholder*='employee'], #txtEmployee (visible: True)
2025-06-03 16:01:12,182 - core.automation_engine - ERROR - Error executing event 10: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-06-03 16:01:12,183 - core.automation_engine - INFO - Executing event 12/26: wait
2025-06-03 16:01:12,183 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:01:15,684 - core.automation_engine - INFO - Executing event 13/26: navigate
2025-06-03 16:01:15,685 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:01:19,762 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589DA9310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/url
2025-06-03 16:01:23,809 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589DA91D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/url
2025-06-03 16:01:27,867 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C6F100>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/url
2025-06-03 16:01:31,940 - core.automation_engine - ERROR - Error executing event 12: HTTPConnectionPool(host='localhost', port=63610): Max retries exceeded with url: /session/f67ca4041113b8801476bba9c4c4de0c/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C6EC40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:01:31,940 - core.automation_engine - INFO - Executing event 14/26: input
2025-06-03 16:01:35,996 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589D92E70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:01:40,057 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C8F570>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:01:44,095 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C8F790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:01:52,198 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589D8EB50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:01:56,294 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:02:00,379 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:02:08,545 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:02:12,587 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:02:16,641 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E846E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:02:24,784 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:02:28,846 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:02:32,917 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:02:36,993 - core.automation_engine - ERROR - Error executing event 13: HTTPConnectionPool(host='localhost', port=63610): Max retries exceeded with url: /session/f67ca4041113b8801476bba9c4c4de0c/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:02:36,994 - core.automation_engine - INFO - Executing event 15/26: keyboard
2025-06-03 16:02:36,994 - core.automation_engine - ERROR - Error executing event 14: Keyboard event requires both selector and key
2025-06-03 16:02:36,994 - core.automation_engine - INFO - Executing event 16/26: wait
2025-06-03 16:02:36,994 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:02:39,496 - core.automation_engine - INFO - Executing event 17/26: keyboard
2025-06-03 16:02:39,497 - core.automation_engine - ERROR - Error executing event 16: Keyboard event requires both selector and key
2025-06-03 16:02:39,498 - core.automation_engine - INFO - Executing event 18/26: keyboard
2025-06-03 16:02:39,498 - core.automation_engine - ERROR - Error executing event 17: Keyboard event requires both selector and key
2025-06-03 16:02:39,499 - core.automation_engine - INFO - Executing event 19/26: input
2025-06-03 16:02:43,553 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:02:47,619 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E854F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:02:51,682 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E856D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:02:59,814 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E859A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:03,918 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:07,952 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:16,137 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E864E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:20,226 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E866C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:24,294 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:03:32,477 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:03:36,544 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:03:40,614 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C936B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:03:44,697 - core.automation_engine - ERROR - Error executing event 18: HTTPConnectionPool(host='localhost', port=63610): Max retries exceeded with url: /session/f67ca4041113b8801476bba9c4c4de0c/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C933E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:03:44,698 - core.automation_engine - INFO - Executing event 20/26: keyboard
2025-06-03 16:03:44,698 - core.automation_engine - ERROR - Error executing event 19: Keyboard event requires both selector and key
2025-06-03 16:03:44,698 - core.automation_engine - INFO - Executing event 21/26: keyboard
2025-06-03 16:03:44,699 - core.automation_engine - ERROR - Error executing event 20: Keyboard event requires both selector and key
2025-06-03 16:03:44,699 - core.automation_engine - INFO - Executing event 22/26: input
2025-06-03 16:03:48,761 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:03:52,873 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C93020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:03:56,944 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C925D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:04:05,111 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589C924E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:09,170 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E86120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:13,258 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E86030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:21,391 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:25,468 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E856D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:29,540 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E854F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/element
2025-06-03 16:04:37,668 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:04:41,745 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E85040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:04:45,814 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/source
2025-06-03 16:04:49,868 - core.automation_engine - ERROR - Error executing event 21: HTTPConnectionPool(host='localhost', port=63610): Max retries exceeded with url: /session/f67ca4041113b8801476bba9c4c4de0c/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:04:49,868 - core.automation_engine - INFO - Executing event 23/26: keyboard
2025-06-03 16:04:49,869 - core.automation_engine - ERROR - Error executing event 22: Keyboard event requires both selector and key
2025-06-03 16:04:49,869 - core.automation_engine - INFO - Executing event 24/26: keyboard
2025-06-03 16:04:49,870 - core.automation_engine - ERROR - Error executing event 23: Keyboard event requires both selector and key
2025-06-03 16:04:49,870 - core.automation_engine - INFO - Executing event 25/26: wait
2025-06-03 16:04:49,871 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:04:53,372 - core.automation_engine - INFO - Executing event 26/26: prevent_redirect
2025-06-03 16:04:53,373 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:04:57,448 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:01,517 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E848C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:05,612 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E847D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:09,650 - core.automation_engine - WARNING - Failed to set up redirect prevention: HTTPConnectionPool(host='localhost', port=63610): Max retries exceeded with url: /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E845F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:05:10,152 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:05:10,152 - main - INFO - Complete Millware flow completed successfully
2025-06-03 16:05:14,225 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E84320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:18,289 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E868A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:22,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E86A80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:24,391 - main - INFO - Cleaning up resources
2025-06-03 16:05:28,478 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E86F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:32,568 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E87110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:36,687 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E872F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:44,880 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E875C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:48,990 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E87980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:05:53,056 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000018589E87B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f67ca4041113b8801476bba9c4c4de0c/execute/sync
2025-06-03 16:08:49,587 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:08:49,588 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:08:50,525 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:08:50,991 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:08:51,398 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:08:52,829 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:08:52,840 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:08:55,598 - __main__ - INFO - Application initialized successfully
2025-06-03 16:08:55,599 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 16:08:55,601 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:08:55,602 - __main__ - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:08:55,603 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:08:55,603 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:08:55,604 - __main__ - INFO - Loaded flow with 21 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:08:55,604 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:08:55,606 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:08:55,607 - core.automation_engine - INFO - Executing event 1/32: navigate
2025-06-03 16:08:55,607 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:08:57,483 - core.automation_engine - INFO - Executing event 2/32: wait_for_element
2025-06-03 16:08:57,824 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:08:58,162 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:08:58,663 - core.automation_engine - INFO - Executing event 3/32: input
2025-06-03 16:08:59,381 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:08:59,881 - core.automation_engine - INFO - Executing event 4/32: input
2025-06-03 16:09:00,602 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:09:01,103 - core.automation_engine - INFO - Executing event 5/32: click
2025-06-03 16:09:04,977 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:09:05,477 - core.automation_engine - INFO - Executing event 6/32: wait
2025-06-03 16:09:05,478 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:09:08,980 - core.automation_engine - INFO - Executing event 7/32: popup_handler
2025-06-03 16:09:08,981 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:09:09,006 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:09:10,357 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:09:13,563 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:09:14,564 - core.automation_engine - INFO - Executing event 8/32: wait
2025-06-03 16:09:14,565 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:09:18,066 - core.automation_engine - INFO - Executing event 9/32: navigate
2025-06-03 16:09:18,067 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:09:20,354 - core.automation_engine - INFO - Executing event 10/32: wait
2025-06-03 16:09:20,354 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 16:09:25,856 - core.automation_engine - INFO - Executing event 11/32: wait
2025-06-03 16:09:25,857 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:09:29,358 - core.automation_engine - INFO - Executing event 12/32: wait_for_element
2025-06-03 16:09:29,712 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate'] (visible: True)
2025-06-03 16:09:30,052 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate']
2025-06-03 16:09:30,553 - core.automation_engine - INFO - Executing event 13/32: input
2025-06-03 16:09:31,474 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:09:31,477 - core.automation_engine - INFO - Executing event 14/32: click
2025-06-03 16:09:32,396 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:09:32,912 - core.automation_engine - INFO - Executing event 15/32: wait
2025-06-03 16:09:32,913 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 16:09:38,415 - core.automation_engine - INFO - Executing event 16/32: wait_for_element
2025-06-03 16:09:38,755 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input'] (visible: True)
2025-06-03 16:09:39,089 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:09:39,590 - core.automation_engine - INFO - Executing event 17/32: prevent_redirect
2025-06-03 16:09:39,591 - core.automation_engine - INFO - Setting up redirect prevention for 60.0 seconds
2025-06-03 16:09:39,600 - core.automation_engine - INFO - Redirect prevention active for 60.0 seconds
2025-06-03 16:09:40,101 - core.automation_engine - INFO - Executing event 18/32: input
2025-06-03 16:09:40,873 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:09:41,374 - core.automation_engine - INFO - Executing event 19/32: keyboard
2025-06-03 16:09:41,374 - core.automation_engine - ERROR - Error executing event 18: Keyboard event requires both selector and key
2025-06-03 16:09:41,374 - core.automation_engine - INFO - Executing event 20/32: wait_for_element
2025-06-03 16:09:41,707 - core.automation_engine - INFO - Waiting for element: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none']) (visible: True)
2025-06-03 16:09:42,039 - core.automation_engine - INFO - Element condition met: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none'])
2025-06-03 16:09:42,539 - core.automation_engine - INFO - Executing event 21/32: keyboard
2025-06-03 16:09:42,541 - core.automation_engine - ERROR - Error executing event 20: Keyboard event requires both selector and key
2025-06-03 16:09:42,541 - core.automation_engine - INFO - Executing event 22/32: keyboard
2025-06-03 16:09:42,542 - core.automation_engine - ERROR - Error executing event 21: Keyboard event requires both selector and key
2025-06-03 16:09:42,543 - core.automation_engine - INFO - Executing event 23/32: wait
2025-06-03 16:09:42,543 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:09:45,045 - core.automation_engine - INFO - Executing event 24/32: input
2025-06-03 16:09:45,160 - core.automation_engine - ERROR - Error executing event 23: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:09:45,161 - core.automation_engine - INFO - Executing event 25/32: keyboard
2025-06-03 16:09:45,161 - core.automation_engine - ERROR - Error executing event 24: Keyboard event requires both selector and key
2025-06-03 16:09:45,161 - core.automation_engine - INFO - Executing event 26/32: keyboard
2025-06-03 16:09:45,161 - core.automation_engine - ERROR - Error executing event 25: Keyboard event requires both selector and key
2025-06-03 16:09:45,161 - core.automation_engine - INFO - Executing event 27/32: wait
2025-06-03 16:09:45,162 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:09:47,663 - core.automation_engine - INFO - Executing event 28/32: input
2025-06-03 16:09:47,787 - core.automation_engine - ERROR - Error executing event 27: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:09:47,787 - core.automation_engine - INFO - Executing event 29/32: keyboard
2025-06-03 16:09:47,788 - core.automation_engine - ERROR - Error executing event 28: Keyboard event requires both selector and key
2025-06-03 16:09:47,788 - core.automation_engine - INFO - Executing event 30/32: keyboard
2025-06-03 16:09:47,788 - core.automation_engine - ERROR - Error executing event 29: Keyboard event requires both selector and key
2025-06-03 16:09:47,788 - core.automation_engine - INFO - Executing event 31/32: wait
2025-06-03 16:09:47,788 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:09:51,289 - core.automation_engine - INFO - Executing event 32/32: prevent_redirect
2025-06-03 16:09:51,289 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:09:51,300 - core.automation_engine - INFO - Redirect prevention active for 3.0 seconds
2025-06-03 16:09:51,800 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:09:51,801 - __main__ - INFO - Complete Millware flow completed successfully
2025-06-03 16:11:19,943 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:11:19,943 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:11:20,870 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:11:21,231 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:11:21,675 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:11:22,276 - __main__ - INFO - Cleaning up resources
2025-06-03 16:11:23,109 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:11:23,133 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:11:24,399 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:11:24,399 - __main__ - INFO - Cleanup completed
2025-06-03 16:11:25,692 - main - INFO - Application initialized successfully
2025-06-03 16:11:45,872 - main - INFO - 🔐 Starting pre-login automation flow
2025-06-03 16:11:45,873 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:11:45,874 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:11:45,874 - main - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:11:45,875 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:11:45,875 - core.automation_engine - INFO - Executing event 1/10: navigate
2025-06-03 16:11:45,875 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:11:47,787 - core.automation_engine - INFO - Executing event 2/10: wait_for_element
2025-06-03 16:11:48,124 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:11:48,466 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:11:48,967 - core.automation_engine - INFO - Executing event 3/10: input
2025-06-03 16:11:49,690 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:11:50,192 - core.automation_engine - INFO - Executing event 4/10: input
2025-06-03 16:11:50,911 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:11:51,412 - core.automation_engine - INFO - Executing event 5/10: click
2025-06-03 16:11:54,765 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:11:55,266 - core.automation_engine - INFO - Executing event 6/10: wait
2025-06-03 16:11:55,267 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:11:58,769 - core.automation_engine - INFO - Executing event 7/10: popup_handler
2025-06-03 16:11:58,769 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:11:58,789 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:12:00,131 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:12:03,935 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:12:04,937 - core.automation_engine - INFO - Executing event 8/10: wait
2025-06-03 16:12:04,938 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:12:08,439 - core.automation_engine - INFO - Executing event 9/10: navigate
2025-06-03 16:12:08,439 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:12:11,225 - core.automation_engine - INFO - Executing event 10/10: wait
2025-06-03 16:12:11,226 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 16:12:16,727 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:12:16,728 - main - INFO - ✅ Pre-login flow completed successfully
2025-06-03 16:12:16,746 - main - INFO - 📋 Starting post-login automation flow
2025-06-03 16:12:16,747 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:12:16,747 - main - INFO - Loaded flow with 21 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:12:16,747 - main - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:12:16,748 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:12:16,748 - core.automation_engine - INFO - Executing event 1/21: wait_for_element
2025-06-03 16:12:17,094 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate'] (visible: True)
2025-06-03 16:12:17,433 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate']
2025-06-03 16:12:17,935 - core.automation_engine - INFO - Executing event 2/21: input
2025-06-03 16:12:19,443 - core.automation_engine - ERROR - Error executing event 1: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:12:19,444 - core.automation_engine - INFO - Executing event 3/21: click
2025-06-03 16:12:20,353 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:12:20,854 - core.automation_engine - INFO - Executing event 4/21: wait
2025-06-03 16:12:20,854 - core.automation_engine - INFO - Waiting for 0.5 seconds
2025-06-03 16:12:21,856 - core.automation_engine - INFO - Executing event 5/21: wait_for_element
2025-06-03 16:12:22,192 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input'] (visible: True)
2025-06-03 16:12:22,532 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:12:23,033 - core.automation_engine - INFO - Executing event 6/21: prevent_redirect
2025-06-03 16:12:23,034 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:12:23,043 - core.automation_engine - INFO - Redirect prevention active for 3.0 seconds
2025-06-03 16:12:23,544 - core.automation_engine - INFO - Executing event 7/21: input
2025-06-03 16:12:24,299 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:12:24,800 - core.automation_engine - INFO - Executing event 8/21: keyboard
2025-06-03 16:12:24,801 - core.automation_engine - ERROR - Error executing event 7: Keyboard event requires both selector and key
2025-06-03 16:12:24,801 - core.automation_engine - INFO - Executing event 9/21: wait_for_element
2025-06-03 16:12:25,143 - core.automation_engine - INFO - Waiting for element: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none']) (visible: True)
2025-06-03 16:12:25,486 - core.automation_engine - INFO - Element condition met: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none'])
2025-06-03 16:12:25,987 - core.automation_engine - INFO - Executing event 10/21: keyboard
2025-06-03 16:12:25,989 - core.automation_engine - ERROR - Error executing event 9: Keyboard event requires both selector and key
2025-06-03 16:12:25,989 - core.automation_engine - INFO - Executing event 11/21: keyboard
2025-06-03 16:12:25,990 - core.automation_engine - ERROR - Error executing event 10: Keyboard event requires both selector and key
2025-06-03 16:12:25,991 - core.automation_engine - INFO - Executing event 12/21: wait
2025-06-03 16:12:25,992 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:12:28,494 - core.automation_engine - INFO - Executing event 13/21: input
2025-06-03 16:12:28,621 - core.automation_engine - ERROR - Error executing event 12: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:12:28,622 - core.automation_engine - INFO - Executing event 14/21: keyboard
2025-06-03 16:12:28,622 - core.automation_engine - ERROR - Error executing event 13: Keyboard event requires both selector and key
2025-06-03 16:12:28,622 - core.automation_engine - INFO - Executing event 15/21: keyboard
2025-06-03 16:12:28,622 - core.automation_engine - ERROR - Error executing event 14: Keyboard event requires both selector and key
2025-06-03 16:12:28,623 - core.automation_engine - INFO - Executing event 16/21: wait
2025-06-03 16:12:28,623 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:12:31,124 - core.automation_engine - INFO - Executing event 17/21: input
2025-06-03 16:12:31,235 - core.automation_engine - ERROR - Error executing event 16: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:12:31,236 - core.automation_engine - INFO - Executing event 18/21: keyboard
2025-06-03 16:12:31,236 - core.automation_engine - ERROR - Error executing event 17: Keyboard event requires both selector and key
2025-06-03 16:12:31,236 - core.automation_engine - INFO - Executing event 19/21: keyboard
2025-06-03 16:12:31,236 - core.automation_engine - ERROR - Error executing event 18: Keyboard event requires both selector and key
2025-06-03 16:12:31,236 - core.automation_engine - INFO - Executing event 20/21: wait
2025-06-03 16:12:31,236 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:12:34,738 - core.automation_engine - INFO - Executing event 21/21: prevent_redirect
2025-06-03 16:12:34,738 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:12:34,744 - core.automation_engine - INFO - Redirect prevention active for 3.0 seconds
2025-06-03 16:12:35,245 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:12:35,245 - main - INFO - ✅ Post-login flow completed successfully
2025-06-03 16:12:39,526 - main - INFO - Cleaning up resources
2025-06-03 16:12:41,657 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:12:41,658 - main - INFO - Cleanup completed
2025-06-03 16:16:13,825 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:16:13,826 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:16:14,726 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:16:15,054 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:16:15,398 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:16:16,820 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:16:16,854 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:16:19,162 - __main__ - INFO - Application initialized successfully
2025-06-03 16:16:19,163 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 16:16:19,164 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:16:19,164 - __main__ - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:16:19,165 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:16:19,166 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:16:19,167 - __main__ - INFO - Loaded flow with 21 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:16:19,167 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:16:19,168 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:16:19,168 - core.automation_engine - INFO - Executing event 1/32: navigate
2025-06-03 16:16:19,168 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:16:20,986 - core.automation_engine - INFO - Executing event 2/32: wait_for_element
2025-06-03 16:16:21,367 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:16:21,704 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:16:22,205 - core.automation_engine - INFO - Executing event 3/32: input
2025-06-03 16:16:22,996 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:16:23,498 - core.automation_engine - INFO - Executing event 4/32: input
2025-06-03 16:16:24,295 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:16:24,796 - core.automation_engine - INFO - Executing event 5/32: click
2025-06-03 16:16:28,849 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:16:29,351 - core.automation_engine - INFO - Executing event 6/32: wait
2025-06-03 16:16:29,352 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:16:32,853 - core.automation_engine - INFO - Executing event 7/32: popup_handler
2025-06-03 16:16:32,854 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:16:32,885 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:16:34,220 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:16:39,091 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:16:40,092 - core.automation_engine - INFO - Executing event 8/32: wait
2025-06-03 16:16:40,093 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:16:43,594 - core.automation_engine - INFO - Executing event 9/32: navigate
2025-06-03 16:16:43,595 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:16:45,925 - core.automation_engine - INFO - Executing event 10/32: wait
2025-06-03 16:16:45,926 - core.automation_engine - INFO - Waiting for 0.5 seconds
2025-06-03 16:16:46,927 - core.automation_engine - INFO - Executing event 11/32: wait
2025-06-03 16:16:46,928 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:16:50,429 - core.automation_engine - INFO - Executing event 12/32: wait_for_element
2025-06-03 16:16:50,796 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate'] (visible: True)
2025-06-03 16:16:51,141 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate']
2025-06-03 16:16:51,642 - core.automation_engine - INFO - Executing event 13/32: input
2025-06-03 16:16:52,471 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:16:52,472 - core.automation_engine - INFO - Executing event 14/32: click
2025-06-03 16:16:53,395 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:16:53,896 - core.automation_engine - INFO - Executing event 15/32: wait
2025-06-03 16:16:53,897 - core.automation_engine - INFO - Waiting for 5.0 seconds
2025-06-03 16:16:59,399 - core.automation_engine - INFO - Executing event 16/32: wait_for_element
2025-06-03 16:16:59,764 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input'] (visible: True)
2025-06-03 16:17:00,094 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:17:00,594 - core.automation_engine - INFO - Executing event 17/32: prevent_redirect
2025-06-03 16:17:00,595 - core.automation_engine - INFO - Setting up redirect prevention for 60.0 seconds
2025-06-03 16:17:00,602 - core.automation_engine - INFO - Redirect prevention active for 60.0 seconds
2025-06-03 16:17:01,102 - core.automation_engine - INFO - Executing event 18/32: input
2025-06-03 16:17:01,861 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:17:02,362 - core.automation_engine - INFO - Executing event 19/32: keyboard
2025-06-03 16:17:03,084 - core.automation_engine - INFO - Sent key 'Space' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:17:05,085 - core.automation_engine - INFO - Executing event 20/32: wait_for_element
2025-06-03 16:17:15,151 - core.automation_engine - INFO - Waiting for element: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none']) (visible: True)
2025-06-03 16:17:25,174 - core.automation_engine - ERROR - Error executing event 19: Element condition not met within 5.0s: .ui-autocomplete.ui-widget.ui-widget-content.ui-corner-all:not([style*='display: none'])
2025-06-03 16:17:25,174 - core.automation_engine - INFO - Executing event 21/32: keyboard
2025-06-03 16:17:25,877 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:17:26,879 - core.automation_engine - INFO - Executing event 22/32: keyboard
2025-06-03 16:17:27,565 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:17:30,065 - core.automation_engine - INFO - Executing event 23/32: wait
2025-06-03 16:17:30,066 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:17:32,567 - core.automation_engine - INFO - Executing event 24/32: input
2025-06-03 16:17:32,716 - core.automation_engine - ERROR - Error executing event 23: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:17:32,717 - core.automation_engine - INFO - Executing event 25/32: keyboard
2025-06-03 16:17:33,461 - core.automation_engine - INFO - Sent key 'ArrowDown' to element using ActionChains: input.ui-autocomplete-input.ui-widget.CBOBox:eq(1), input[class*='ui-autocomplete-input']:eq(1)
2025-06-03 16:17:34,462 - core.automation_engine - INFO - Executing event 26/32: keyboard
2025-06-03 16:17:35,232 - core.automation_engine - INFO - Sent key 'Enter' to element using ActionChains: input.ui-autocomplete-input.ui-widget.CBOBox:eq(1), input[class*='ui-autocomplete-input']:eq(1)
2025-06-03 16:17:37,734 - core.automation_engine - INFO - Executing event 27/32: wait
2025-06-03 16:17:37,735 - core.automation_engine - INFO - Waiting for 2.0 seconds
2025-06-03 16:17:40,236 - core.automation_engine - INFO - Executing event 28/32: input
2025-06-03 16:17:40,384 - core.automation_engine - ERROR - Error executing event 27: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:17:40,384 - core.automation_engine - INFO - Executing event 29/32: keyboard
2025-06-03 16:17:41,104 - core.automation_engine - INFO - Sent key 'ArrowDown' to element using ActionChains: input.ui-autocomplete-input.ui-widget.CBOBox:eq(2), input[class*='ui-autocomplete-input']:eq(2)
2025-06-03 16:17:42,105 - core.automation_engine - INFO - Executing event 30/32: keyboard
2025-06-03 16:17:42,879 - core.automation_engine - INFO - Sent key 'Enter' to element using ActionChains: input.ui-autocomplete-input.ui-widget.CBOBox:eq(2), input[class*='ui-autocomplete-input']:eq(2)
2025-06-03 16:17:45,380 - core.automation_engine - INFO - Executing event 31/32: wait
2025-06-03 16:17:45,381 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:17:48,882 - core.automation_engine - INFO - Executing event 32/32: prevent_redirect
2025-06-03 16:17:48,883 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:17:48,885 - core.automation_engine - WARNING - Failed to set up redirect prevention: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007CFE20]
	(No symbol) [0x007EDD1F]
	(No symbol) [0x00853E8C]
	(No symbol) [0x0086DF19]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:17:49,386 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:17:49,387 - __main__ - INFO - Complete Millware flow completed successfully
2025-06-03 16:25:59,799 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:25:59,799 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:26:00,737 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:26:01,083 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:26:01,418 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:26:02,860 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:26:02,875 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:26:04,922 - main - INFO - Application initialized successfully
2025-06-03 16:26:24,997 - main - INFO - 🔐 Starting pre-login automation flow
2025-06-03 16:26:24,998 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:26:24,999 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:26:24,999 - main - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:26:24,999 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:26:24,999 - core.automation_engine - INFO - Executing event 1/10: navigate
2025-06-03 16:26:25,000 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:26:26,874 - core.automation_engine - INFO - Executing event 2/10: wait_for_element
2025-06-03 16:26:27,221 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:26:27,566 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:26:28,067 - core.automation_engine - INFO - Executing event 3/10: input
2025-06-03 16:26:28,782 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:26:29,283 - core.automation_engine - INFO - Executing event 4/10: input
2025-06-03 16:26:30,016 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:26:30,517 - core.automation_engine - INFO - Executing event 5/10: click
2025-06-03 16:26:33,563 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:26:34,064 - core.automation_engine - INFO - Executing event 6/10: wait
2025-06-03 16:26:34,065 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:26:36,066 - core.automation_engine - INFO - Executing event 7/10: popup_handler
2025-06-03 16:26:36,067 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:26:36,087 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:26:37,433 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:26:42,430 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:26:43,432 - core.automation_engine - INFO - Executing event 8/10: wait
2025-06-03 16:26:43,433 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:26:44,933 - core.automation_engine - INFO - Executing event 9/10: navigate
2025-06-03 16:26:44,934 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:26:47,285 - core.automation_engine - INFO - Executing event 10/10: wait
2025-06-03 16:26:47,286 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:26:48,787 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:26:48,788 - main - INFO - ✅ Pre-login flow completed successfully
2025-06-03 16:26:48,806 - main - INFO - 📋 Starting post-login automation flow
2025-06-03 16:26:48,807 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:26:48,808 - main - INFO - Loaded flow with 20 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:26:48,808 - main - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:26:48,808 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:26:48,809 - core.automation_engine - INFO - Executing event 1/20: wait_for_element
2025-06-03 16:26:49,166 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate'] (visible: True)
2025-06-03 16:26:49,509 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate, input[name='ctl00$MainContent$txtTrxDate']
2025-06-03 16:26:50,010 - core.automation_engine - INFO - Executing event 2/20: input
2025-06-03 16:26:50,816 - core.automation_engine - ERROR - Error executing event 1: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:26:50,817 - core.automation_engine - INFO - Executing event 3/20: click
2025-06-03 16:26:51,730 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:26:52,231 - core.automation_engine - INFO - Executing event 4/20: wait
2025-06-03 16:26:52,231 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:26:54,232 - core.automation_engine - INFO - Executing event 5/20: wait_for_element
2025-06-03 16:26:54,565 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input'] (visible: True)
2025-06-03 16:26:54,916 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:26:55,416 - core.automation_engine - INFO - Executing event 6/20: prevent_redirect
2025-06-03 16:26:55,417 - core.automation_engine - INFO - Setting up redirect prevention for 30.0 seconds
2025-06-03 16:26:55,422 - core.automation_engine - INFO - Redirect prevention active for 30.0 seconds
2025-06-03 16:26:55,922 - core.automation_engine - INFO - Executing event 7/20: input
2025-06-03 16:26:56,647 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:26:57,148 - core.automation_engine - INFO - Executing event 8/20: keyboard
2025-06-03 16:26:57,823 - core.automation_engine - INFO - Sent key 'Space' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:26:59,325 - core.automation_engine - INFO - Executing event 9/20: keyboard
2025-06-03 16:26:59,993 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:27:00,994 - core.automation_engine - INFO - Executing event 10/20: keyboard
2025-06-03 16:27:01,678 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.ui-widget.CBOBox, input[class*='ui-autocomplete-input']
2025-06-03 16:27:03,680 - core.automation_engine - INFO - Executing event 11/20: wait_for_element
2025-06-03 16:27:03,703 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox:eq(1), input[class*='ui-autocomplete-input']:eq(1) (visible: True)
2025-06-03 16:27:03,708 - core.automation_engine - ERROR - Error executing event 10: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x00828234]
	(No symbol) [0x00828D3B]
	(No symbol) [0x00870E12]
	(No symbol) [0x0084D2E4]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:03,708 - core.automation_engine - INFO - Executing event 12/20: input
2025-06-03 16:27:03,804 - core.automation_engine - ERROR - Error executing event 11: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:03,804 - core.automation_engine - INFO - Executing event 13/20: keyboard
2025-06-03 16:27:04,214 - core.automation_engine - ERROR - Error executing event 12: Failed to send key 'ArrowDown' to element: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082E3E5]
	(No symbol) [0x0082D937]
	(No symbol) [0x00876D54]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:04,214 - core.automation_engine - INFO - Executing event 14/20: keyboard
2025-06-03 16:27:04,916 - core.automation_engine - INFO - Sent key 'Enter' to element using ActionChains: input.ui-autocomplete-input.ui-widget.CBOBox:eq(1), input[class*='ui-autocomplete-input']:eq(1)
2025-06-03 16:27:06,418 - core.automation_engine - INFO - Executing event 15/20: wait_for_element
2025-06-03 16:27:06,444 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.ui-widget.CBOBox:eq(2), input[class*='ui-autocomplete-input']:eq(2) (visible: True)
2025-06-03 16:27:06,450 - core.automation_engine - ERROR - Error executing event 14: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x00828234]
	(No symbol) [0x00828D3B]
	(No symbol) [0x00870E12]
	(No symbol) [0x0084D2E4]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:06,450 - core.automation_engine - INFO - Executing event 16/20: input
2025-06-03 16:27:06,539 - core.automation_engine - ERROR - Error executing event 15: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:06,539 - core.automation_engine - INFO - Executing event 17/20: keyboard
2025-06-03 16:27:06,945 - core.automation_engine - ERROR - Error executing event 16: Failed to send key 'ArrowDown' to element: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082E3E5]
	(No symbol) [0x0082D937]
	(No symbol) [0x00876D54]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:06,945 - core.automation_engine - INFO - Executing event 18/20: keyboard
2025-06-03 16:27:07,356 - core.automation_engine - WARNING - Could not set preventDefault for key event: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E9335]
	(No symbol) [0x0086F531]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:07,365 - core.automation_engine - ERROR - Error executing event 17: Failed to send key 'Enter' to element: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007E6DD0]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082E3E5]
	(No symbol) [0x0082D937]
	(No symbol) [0x00876D54]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:27:07,374 - core.automation_engine - INFO - Executing event 19/20: wait
2025-06-03 16:27:07,375 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:27:08,876 - core.automation_engine - INFO - Executing event 20/20: prevent_redirect
2025-06-03 16:27:08,876 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:27:08,884 - core.automation_engine - INFO - Redirect prevention active for 3.0 seconds
2025-06-03 16:27:09,385 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:27:09,386 - main - INFO - ✅ Post-login flow completed successfully
2025-06-03 16:28:05,135 - __main__ - INFO - Cleaning up resources
2025-06-03 16:28:07,177 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:28:07,177 - __main__ - INFO - Cleanup completed
2025-06-03 16:28:08,449 - main - INFO - Cleaning up resources
2025-06-03 16:28:12,509 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011284ABC590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:16,593 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011284A0CB90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:20,656 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011284A0E0D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:28,800 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112848D2EA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:32,858 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001128708FD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:36,963 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112848FB8A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:45,122 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112870A1250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:49,229 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112870A0650>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:28:53,310 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112848FF110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb/execute/sync
2025-06-03 16:29:01,425 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112848FF980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb
2025-06-03 16:29:05,493 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000112848FFF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb
2025-06-03 16:29:09,582 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000011284CDC140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/78490cd535ce146195023ac937a7cebb
2025-06-03 16:29:13,673 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:29:13,673 - main - INFO - Cleanup completed
2025-06-03 16:36:03,560 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:36:03,561 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:36:04,381 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:36:04,697 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:36:05,059 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:36:06,462 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:36:06,484 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:36:09,002 - __main__ - INFO - Application initialized successfully
2025-06-03 16:36:09,003 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 16:36:09,004 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:36:09,004 - __main__ - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:36:09,004 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:36:09,005 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:36:09,005 - __main__ - INFO - Loaded flow with 20 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:36:09,006 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:36:09,006 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:36:09,006 - core.automation_engine - INFO - Executing event 1/31: navigate
2025-06-03 16:36:09,006 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:36:10,867 - core.automation_engine - INFO - Executing event 2/31: wait_for_element
2025-06-03 16:36:11,206 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:36:11,553 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:36:12,054 - core.automation_engine - INFO - Executing event 3/31: input
2025-06-03 16:36:12,770 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:36:13,271 - core.automation_engine - INFO - Executing event 4/31: input
2025-06-03 16:36:14,114 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:36:14,615 - core.automation_engine - INFO - Executing event 5/31: click
2025-06-03 16:36:18,739 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:36:19,240 - core.automation_engine - INFO - Executing event 6/31: wait
2025-06-03 16:36:19,241 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:36:21,243 - core.automation_engine - INFO - Executing event 7/31: popup_handler
2025-06-03 16:36:21,244 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:36:21,259 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:36:22,609 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:36:27,504 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:36:28,506 - core.automation_engine - INFO - Executing event 8/31: wait
2025-06-03 16:36:28,507 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:36:30,009 - core.automation_engine - INFO - Executing event 9/31: navigate
2025-06-03 16:36:30,009 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:36:32,424 - core.automation_engine - INFO - Executing event 10/31: wait
2025-06-03 16:36:32,425 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:36:33,926 - core.automation_engine - INFO - Executing event 11/31: wait
2025-06-03 16:36:33,927 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:36:37,428 - core.automation_engine - INFO - Executing event 12/31: wait_for_element
2025-06-03 16:36:37,778 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 16:36:38,110 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 16:36:38,620 - core.automation_engine - INFO - Executing event 13/31: input
2025-06-03 16:36:39,438 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:36:39,439 - core.automation_engine - INFO - Executing event 14/31: click
2025-06-03 16:36:40,397 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:36:40,898 - core.automation_engine - INFO - Executing event 15/31: wait
2025-06-03 16:36:40,899 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:36:42,900 - core.automation_engine - INFO - Executing event 16/31: wait_for_element
2025-06-03 16:36:43,239 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 16:36:43,572 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 16:36:44,073 - core.automation_engine - INFO - Executing event 17/31: prevent_redirect
2025-06-03 16:36:44,073 - core.automation_engine - INFO - Setting up redirect prevention for 30.0 seconds
2025-06-03 16:36:44,079 - core.automation_engine - INFO - Redirect prevention active for 30.0 seconds
2025-06-03 16:36:44,580 - core.automation_engine - INFO - Executing event 18/31: input
2025-06-03 16:36:45,389 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:36:45,890 - core.automation_engine - INFO - Executing event 19/31: keyboard
2025-06-03 16:36:46,603 - core.automation_engine - INFO - Sent key 'Space' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:36:48,104 - core.automation_engine - INFO - Executing event 20/31: keyboard
2025-06-03 16:36:48,793 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:36:49,795 - core.automation_engine - INFO - Executing event 21/31: keyboard
2025-06-03 16:36:50,529 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:36:52,531 - core.automation_engine - INFO - Executing event 22/31: wait_for_element
2025-06-03 16:37:02,585 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(2) (visible: True)
2025-06-03 16:37:06,496 - core.automation_engine - ERROR - Error executing event 21: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-06-03 16:37:06,496 - core.automation_engine - INFO - Executing event 23/31: input
2025-06-03 16:37:10,537 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE6919310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/execute/sync
2025-06-03 16:37:14,577 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE69191D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/execute/sync
2025-06-03 16:37:18,618 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67E6FD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/execute/sync
2025-06-03 16:37:26,792 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE6902E70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:30,828 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67F7460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:34,871 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67F7680>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:43,012 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE689ED50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:47,102 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67FFD40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:51,157 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67FFF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/element
2025-06-03 16:37:59,292 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE67FF980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/source
2025-06-03 16:38:03,347 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DEE69E45F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/46383f7fd354248d6c39339a6d1f7036/source
2025-06-03 16:38:05,367 - __main__ - INFO - Cleaning up resources
2025-06-03 16:38:19,786 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:38:19,787 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:38:20,598 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:38:20,916 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:38:21,266 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:38:22,731 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:38:22,745 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:38:24,992 - __main__ - INFO - Application initialized successfully
2025-06-03 16:38:24,993 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 16:38:24,994 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:38:24,994 - __main__ - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:38:24,995 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:38:24,995 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:38:24,996 - __main__ - INFO - Loaded flow with 20 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:38:24,996 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:38:24,996 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:38:24,997 - core.automation_engine - INFO - Executing event 1/31: navigate
2025-06-03 16:38:24,997 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:38:26,870 - core.automation_engine - INFO - Executing event 2/31: wait_for_element
2025-06-03 16:38:27,220 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:38:27,554 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:38:28,055 - core.automation_engine - INFO - Executing event 3/31: input
2025-06-03 16:38:28,764 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:38:29,265 - core.automation_engine - INFO - Executing event 4/31: input
2025-06-03 16:38:30,013 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:38:30,514 - core.automation_engine - INFO - Executing event 5/31: click
2025-06-03 16:38:31,432 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:38:31,933 - core.automation_engine - INFO - Executing event 6/31: wait
2025-06-03 16:38:31,933 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:38:33,934 - core.automation_engine - INFO - Executing event 7/31: popup_handler
2025-06-03 16:38:33,935 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:38:34,364 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:38:35,702 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:38:39,797 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:38:40,799 - core.automation_engine - INFO - Executing event 8/31: wait
2025-06-03 16:38:40,799 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:38:42,301 - core.automation_engine - INFO - Executing event 9/31: navigate
2025-06-03 16:38:42,302 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:38:44,766 - core.automation_engine - INFO - Executing event 10/31: wait
2025-06-03 16:38:44,766 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:38:46,268 - core.automation_engine - INFO - Executing event 11/31: wait
2025-06-03 16:38:46,269 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:38:49,770 - core.automation_engine - INFO - Executing event 12/31: wait_for_element
2025-06-03 16:38:50,108 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 16:38:50,438 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 16:38:50,939 - core.automation_engine - INFO - Executing event 13/31: input
2025-06-03 16:38:51,740 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:38:51,741 - core.automation_engine - INFO - Executing event 14/31: click
2025-06-03 16:38:52,676 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:38:53,176 - core.automation_engine - INFO - Executing event 15/31: wait
2025-06-03 16:38:53,177 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:38:55,178 - core.automation_engine - INFO - Executing event 16/31: wait_for_element
2025-06-03 16:38:55,516 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 16:38:55,844 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 16:38:56,345 - core.automation_engine - INFO - Executing event 17/31: prevent_redirect
2025-06-03 16:38:56,345 - core.automation_engine - INFO - Setting up redirect prevention for 30.0 seconds
2025-06-03 16:38:56,351 - core.automation_engine - INFO - Redirect prevention active for 30.0 seconds
2025-06-03 16:38:56,852 - core.automation_engine - INFO - Executing event 18/31: input
2025-06-03 16:38:57,590 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:38:58,091 - core.automation_engine - INFO - Executing event 19/31: keyboard
2025-06-03 16:38:58,764 - core.automation_engine - INFO - Sent key 'Space' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:39:00,265 - core.automation_engine - INFO - Executing event 20/31: keyboard
2025-06-03 16:39:00,950 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:39:01,951 - core.automation_engine - INFO - Executing event 21/31: keyboard
2025-06-03 16:39:02,628 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:39:04,630 - core.automation_engine - INFO - Executing event 22/31: wait_for_element
2025-06-03 16:39:14,721 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(2) (visible: True)
2025-06-03 16:39:18,775 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7CA9310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:39:22,815 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7CA91D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:39:26,870 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B76FD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:39:30,932 - core.automation_engine - ERROR - Error executing event 21: HTTPConnectionPool(host='localhost', port=53301): Max retries exceeded with url: /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B755B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:39:30,932 - core.automation_engine - INFO - Executing event 23/31: input
2025-06-03 16:39:35,002 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7C928D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:39:39,042 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B87460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:39:43,101 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B87680>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:39:51,255 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7C2ED50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:39:55,327 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B8FC50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:39:59,400 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7B8FE30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:40:05,491 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7C410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:40:09,555 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7C5F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/element
2025-06-03 16:40:11,578 - __main__ - INFO - Cleaning up resources
2025-06-03 16:40:15,620 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7C320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:19,677 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7CC80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:23,725 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7CE60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:31,810 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7D130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:35,895 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7D4F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:39,959 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7D6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:48,095 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7D9A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:52,151 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7DD60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:40:56,226 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7DF40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6/execute/sync
2025-06-03 16:41:04,352 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7E210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6
2025-06-03 16:41:08,437 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7E5D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6
2025-06-03 16:41:12,532 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000153B7D7E7B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e21ebf5390e4e5fb13e0c664cb1cf2a6
2025-06-03 16:41:16,604 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:41:16,605 - __main__ - INFO - Cleanup completed
2025-06-03 16:41:35,409 - __main__ - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:41:35,409 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:41:36,230 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:41:36,607 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:41:36,924 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:41:38,416 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:41:38,447 - __main__ - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:41:40,880 - __main__ - INFO - Application initialized successfully
2025-06-03 16:41:40,881 - __main__ - INFO - Starting complete Millware automation flow
2025-06-03 16:41:40,882 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:41:40,883 - __main__ - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:41:40,883 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:41:40,885 - __main__ - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:41:40,886 - __main__ - INFO - Loaded flow with 17 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:41:40,886 - __main__ - INFO - Applied form data: Date=03/06/2025, Employee=Septian Pratama
2025-06-03 16:41:40,887 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:41:40,888 - core.automation_engine - INFO - Executing event 1/28: navigate
2025-06-03 16:41:40,888 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:41:42,705 - core.automation_engine - INFO - Executing event 2/28: wait_for_element
2025-06-03 16:41:43,087 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:41:43,442 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:41:43,943 - core.automation_engine - INFO - Executing event 3/28: input
2025-06-03 16:41:44,668 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:41:45,169 - core.automation_engine - INFO - Executing event 4/28: input
2025-06-03 16:41:45,948 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:41:46,449 - core.automation_engine - INFO - Executing event 5/28: click
2025-06-03 16:41:50,739 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:41:51,241 - core.automation_engine - INFO - Executing event 6/28: wait
2025-06-03 16:41:51,241 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:41:53,243 - core.automation_engine - INFO - Executing event 7/28: popup_handler
2025-06-03 16:41:53,245 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:41:53,281 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:41:54,627 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:41:59,019 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:42:00,021 - core.automation_engine - INFO - Executing event 8/28: wait
2025-06-03 16:42:00,021 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:42:01,523 - core.automation_engine - INFO - Executing event 9/28: navigate
2025-06-03 16:42:01,523 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:42:03,920 - core.automation_engine - INFO - Executing event 10/28: wait
2025-06-03 16:42:03,921 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:42:05,422 - core.automation_engine - INFO - Executing event 11/28: wait
2025-06-03 16:42:05,423 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:42:08,924 - core.automation_engine - INFO - Executing event 12/28: wait_for_element
2025-06-03 16:42:09,263 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 16:42:09,601 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 16:42:10,102 - core.automation_engine - INFO - Executing event 13/28: input
2025-06-03 16:42:10,978 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:42:10,979 - core.automation_engine - INFO - Executing event 14/28: click
2025-06-03 16:42:11,918 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:42:12,419 - core.automation_engine - INFO - Executing event 15/28: wait
2025-06-03 16:42:12,420 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:42:14,421 - core.automation_engine - INFO - Executing event 16/28: input
2025-06-03 16:42:15,224 - core.automation_engine - INFO - Input value 'Septian Pratama' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:42:15,725 - core.automation_engine - INFO - Executing event 17/28: keyboard
2025-06-03 16:42:16,422 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:42:17,423 - core.automation_engine - INFO - Executing event 18/28: keyboard
2025-06-03 16:42:18,123 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:42:20,124 - core.automation_engine - INFO - Executing event 19/28: wait_for_element
2025-06-03 16:42:30,189 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(2) (visible: True)
2025-06-03 16:42:40,215 - core.automation_engine - ERROR - Error executing event 18: Element condition not met within 5.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(2)
2025-06-03 16:42:40,215 - core.automation_engine - INFO - Executing event 20/28: input
2025-06-03 16:43:00,391 - core.automation_engine - ERROR - Error executing event 19: Input element not found: input.ui-autocomplete-input.CBOBox:nth-of-type(2)
2025-06-03 16:43:00,392 - core.automation_engine - INFO - Executing event 21/28: keyboard
2025-06-03 16:43:17,050 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D287C91D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:43:21,083 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D287CA210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:43:25,180 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286976F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:43:33,353 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A4F650>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:43:37,412 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286A7AC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:43:41,465 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286A7CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:43:45,530 - core.automation_engine - ERROR - Error executing event 20: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A5DD50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:43:45,531 - core.automation_engine - INFO - Executing event 22/28: keyboard
2025-06-03 16:43:49,597 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A5D250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:43:53,643 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:43:57,717 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:44:05,823 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:09,911 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:14,007 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A655E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:22,169 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:26,238 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:30,308 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AFF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:44:38,447 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AEF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:44:42,509 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A655E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:44:46,548 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:44:50,596 - core.automation_engine - ERROR - Error executing event 21: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:44:50,596 - core.automation_engine - INFO - Executing event 23/28: wait_for_element
2025-06-03 16:44:54,661 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:44:58,743 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:45:02,874 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:45:10,995 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A649B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:15,071 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A647D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:19,127 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:23,209 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-03 16:45:27,251 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:31,310 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:35,402 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:45:39,457 - core.automation_engine - ERROR - Error executing event 22: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A663F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:45:39,457 - core.automation_engine - INFO - Executing event 24/28: input
2025-06-03 16:45:43,514 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A664E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:45:47,590 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A668A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:45:51,657 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66A80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:45:59,795 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:03,851 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:07,928 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A672F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:16,114 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:20,168 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:24,217 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:46:32,327 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:46:36,382 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:46:40,457 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:46:44,576 - core.automation_engine - ERROR - Error executing event 23: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB86E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:46:44,576 - core.automation_engine - INFO - Executing event 25/28: keyboard
2025-06-03 16:46:48,649 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB87D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:46:52,733 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:46:56,799 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:47:04,935 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:09,002 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB9310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:13,071 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB94F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:21,193 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:25,273 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:29,358 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:47:37,500 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A674D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:47:41,605 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:47:45,683 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A67020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:47:49,776 - core.automation_engine - ERROR - Error executing event 24: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:47:49,776 - core.automation_engine - INFO - Executing event 26/28: keyboard
2025-06-03 16:47:53,846 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66C60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:47:57,923 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A668A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:48:02,020 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A664E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:48:10,192 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A666C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:14,291 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A66030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:18,345 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:26,460 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:30,511 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:34,604 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A648C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/element
2025-06-03 16:48:42,792 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:48:46,867 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A64D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:48:50,953 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/source
2025-06-03 16:48:55,017 - core.automation_engine - ERROR - Error executing event 25: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:48:55,018 - core.automation_engine - INFO - Executing event 27/28: wait
2025-06-03 16:48:55,018 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:48:56,520 - core.automation_engine - INFO - Executing event 28/28: prevent_redirect
2025-06-03 16:48:56,521 - core.automation_engine - INFO - Setting up redirect prevention for 3.0 seconds
2025-06-03 16:49:00,610 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:49:04,671 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A656D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:49:08,744 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AEF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:49:12,818 - core.automation_engine - WARNING - Failed to set up redirect prevention: HTTPConnectionPool(host='localhost', port=53976): Max retries exceeded with url: /session/6835fac43976e67837ab2a61f872f068/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AFF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:49:13,318 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:49:13,319 - __main__ - INFO - Complete Millware flow completed successfully
2025-06-03 16:49:17,357 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AFE30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:49:21,421 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:49:25,480 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:51:52,739 - __main__ - INFO - Cleaning up resources
2025-06-03 16:51:56,817 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB89B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:00,877 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB85F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:04,938 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:13,088 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB8230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:17,140 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB99A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:21,258 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB9B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:29,431 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28AB9E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:30,700 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:52:30,701 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:52:31,511 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:52:31,864 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:52:32,166 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:52:33,534 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AFE30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:33,675 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:52:33,708 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:52:36,323 - main - INFO - Application initialized successfully
2025-06-03 16:52:36,324 - main - INFO - Starting complete Millware automation flow
2025-06-03 16:52:36,326 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:52:36,327 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:52:36,327 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 16:52:36,328 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:52:36,329 - main - INFO - Loaded flow with 25 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:52:36,329 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 16:52:36,329 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:52:36,330 - core.automation_engine - INFO - Executing event 1/36: navigate
2025-06-03 16:52:36,330 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:52:37,616 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D286AEF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068/execute/sync
2025-06-03 16:52:38,148 - core.automation_engine - INFO - Executing event 2/36: wait_for_element
2025-06-03 16:52:38,519 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:52:38,851 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:52:39,352 - core.automation_engine - INFO - Executing event 3/36: input
2025-06-03 16:52:40,081 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:52:40,582 - core.automation_engine - INFO - Executing event 4/36: input
2025-06-03 16:52:41,302 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:52:41,803 - core.automation_engine - INFO - Executing event 5/36: click
2025-06-03 16:52:45,788 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A658B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068
2025-06-03 16:52:46,346 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:52:46,847 - core.automation_engine - INFO - Executing event 6/36: wait
2025-06-03 16:52:46,847 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:52:48,849 - core.automation_engine - INFO - Executing event 7/36: popup_handler
2025-06-03 16:52:48,849 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:52:48,870 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:52:49,857 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068
2025-06-03 16:52:50,224 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:52:53,924 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000012D28A65130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/6835fac43976e67837ab2a61f872f068
2025-06-03 16:52:55,663 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:52:56,664 - core.automation_engine - INFO - Executing event 8/36: wait
2025-06-03 16:52:56,665 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:52:57,996 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-03 16:52:57,997 - __main__ - INFO - Cleanup completed
2025-06-03 16:52:58,166 - core.automation_engine - INFO - Executing event 9/36: navigate
2025-06-03 16:52:58,167 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:53:00,614 - core.automation_engine - INFO - Executing event 10/36: wait
2025-06-03 16:53:00,615 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:53:02,116 - core.automation_engine - INFO - Executing event 11/36: wait
2025-06-03 16:53:02,116 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 16:53:05,617 - core.automation_engine - INFO - Executing event 12/36: wait_for_element
2025-06-03 16:53:05,968 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 16:53:06,300 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 16:53:06,801 - core.automation_engine - INFO - Executing event 13/36: input
2025-06-03 16:53:07,651 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:53:07,652 - core.automation_engine - INFO - Executing event 14/36: click
2025-06-03 16:53:08,609 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:53:09,110 - core.automation_engine - INFO - Executing event 15/36: wait
2025-06-03 16:53:09,110 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:53:11,112 - core.automation_engine - INFO - Executing event 16/36: wait_for_element
2025-06-03 16:53:11,448 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 16:53:11,778 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 16:53:12,278 - core.automation_engine - INFO - Executing event 17/36: input
2025-06-03 16:53:13,042 - core.automation_engine - INFO - Input value 'SEPTIAN ADE PRATAMA' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:53:13,543 - core.automation_engine - INFO - Executing event 18/36: keyboard
2025-06-03 16:53:14,216 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:53:15,218 - core.automation_engine - INFO - Executing event 19/36: keyboard
2025-06-03 16:53:15,906 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:53:17,907 - core.automation_engine - INFO - Executing event 20/36: wait_for_element
2025-06-03 16:53:27,950 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(2) (visible: True)
2025-06-03 16:53:37,988 - core.automation_engine - ERROR - Error executing event 19: Element condition not met within 5.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(2)
2025-06-03 16:53:37,988 - core.automation_engine - INFO - Executing event 21/36: input
2025-06-03 16:53:58,133 - core.automation_engine - ERROR - Error executing event 20: Input element not found: input.ui-autocomplete-input.CBOBox:nth-of-type(2)
2025-06-03 16:53:58,133 - core.automation_engine - INFO - Executing event 22/36: keyboard
2025-06-03 16:54:18,299 - core.automation_engine - ERROR - Error executing event 21: Element not found for keyboard input: input.ui-autocomplete-input.CBOBox:nth-of-type(2)
2025-06-03 16:54:18,300 - core.automation_engine - INFO - Executing event 23/36: keyboard
2025-06-03 16:54:30,286 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293741AA350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:54:34,355 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293741AA710>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:54:38,418 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937406F820>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:54:46,516 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937461B9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:54:50,572 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937408FCE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:54:54,615 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937408FF00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:54:58,693 - core.automation_engine - ERROR - Error executing event 22: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374628E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:54:58,694 - core.automation_engine - INFO - Executing event 24/36: wait_for_element
2025-06-03 16:55:02,734 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374613E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:55:06,782 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:55:10,836 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:55:18,909 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374450D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:22,951 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744518B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:27,015 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:31,070 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-03 16:55:35,126 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:39,187 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:43,237 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:55:47,297 - core.automation_engine - ERROR - Error executing event 23: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744524E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:55:47,298 - core.automation_engine - INFO - Executing event 25/36: input
2025-06-03 16:55:51,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744525D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:55:55,406 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:55:59,464 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452B70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:56:07,594 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:11,674 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:15,744 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744533E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:23,886 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:27,971 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:32,048 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:56:40,157 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744536B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:56:44,244 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:56:48,310 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746785F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:56:52,364 - core.automation_engine - ERROR - Error executing event 24: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746787D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:56:52,364 - core.automation_engine - INFO - Executing event 26/36: keyboard
2025-06-03 16:56:56,449 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746788C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:57:00,494 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:57:04,567 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:57:12,682 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:16,775 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:20,861 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746795E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:28,995 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:33,033 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:37,106 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:57:45,275 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:57:49,356 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:57:53,422 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744537A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:57:57,477 - core.automation_engine - ERROR - Error executing event 25: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744535C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:57:57,492 - core.automation_engine - INFO - Executing event 27/36: keyboard
2025-06-03 16:58:01,549 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744533E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:58:05,632 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744532F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:58:09,709 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:58:10,008 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 16:58:10,009 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 16:58:11,029 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:58:11,360 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 16:58:11,683 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 16:58:13,132 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 16:58:13,143 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:58:15,305 - main - INFO - Application initialized successfully
2025-06-03 16:58:17,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:21,931 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452A80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:25,990 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744528A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:34,115 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:35,392 - main - INFO - 🔐 Starting pre-login automation flow
2025-06-03 16:58:35,393 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:58:35,393 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 16:58:35,394 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 16:58:35,394 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:58:35,394 - core.automation_engine - INFO - Executing event 1/10: navigate
2025-06-03 16:58:35,394 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 16:58:37,339 - core.automation_engine - INFO - Executing event 2/10: wait_for_element
2025-06-03 16:58:38,208 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:38,407 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 16:58:38,769 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 16:58:39,271 - core.automation_engine - INFO - Executing event 3/10: input
2025-06-03 16:58:40,124 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 16:58:40,626 - core.automation_engine - INFO - Executing event 4/10: input
2025-06-03 16:58:41,421 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 16:58:41,921 - core.automation_engine - INFO - Executing event 5/10: click
2025-06-03 16:58:42,289 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:58:45,975 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 16:58:46,476 - core.automation_engine - INFO - Executing event 6/10: wait
2025-06-03 16:58:46,477 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:58:48,479 - core.automation_engine - INFO - Executing event 7/10: popup_handler
2025-06-03 16:58:48,480 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 16:58:48,509 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 16:58:49,854 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 16:58:50,445 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:58:54,369 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 16:58:54,501 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374450D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:58:55,370 - core.automation_engine - INFO - Executing event 8/10: wait
2025-06-03 16:58:55,371 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:58:56,872 - core.automation_engine - INFO - Executing event 9/10: navigate
2025-06-03 16:58:56,873 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 16:58:58,582 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374450B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 16:58:59,316 - core.automation_engine - INFO - Executing event 10/10: wait
2025-06-03 16:58:59,317 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 16:59:00,817 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 16:59:00,818 - main - INFO - ✅ Pre-login flow completed successfully
2025-06-03 16:59:00,863 - main - INFO - 📋 Starting post-login automation flow
2025-06-03 16:59:00,865 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 16:59:00,865 - main - INFO - Loaded flow with 27 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 16:59:00,866 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 16:59:00,866 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 16:59:00,867 - core.automation_engine - INFO - Executing event 1/27: wait_for_element
2025-06-03 16:59:01,256 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 16:59:01,647 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 16:59:02,148 - core.automation_engine - INFO - Executing event 2/27: input
2025-06-03 16:59:02,692 - core.automation_engine - ERROR - Error executing event 26: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374450E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:59:02,693 - core.automation_engine - INFO - Executing event 28/36: wait_for_element
2025-06-03 16:59:03,260 - core.automation_engine - ERROR - Error executing event 1: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:59:03,261 - core.automation_engine - INFO - Executing event 3/27: click
2025-06-03 16:59:04,286 - core.automation_engine - INFO - Clicked element: body
2025-06-03 16:59:04,788 - core.automation_engine - INFO - Executing event 4/27: wait
2025-06-03 16:59:04,788 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 16:59:06,789 - core.automation_engine - INFO - Executing event 5/27: wait_for_element
2025-06-03 16:59:06,798 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:59:07,135 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 16:59:07,475 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 16:59:07,976 - core.automation_engine - INFO - Executing event 6/27: input
2025-06-03 16:59:08,732 - core.automation_engine - INFO - Input value 'SEPTIAN ADE PRATAMA' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:59:09,233 - core.automation_engine - INFO - Executing event 7/27: keyboard
2025-06-03 16:59:09,901 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:59:10,880 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:59:10,903 - core.automation_engine - INFO - Executing event 8/27: keyboard
2025-06-03 16:59:11,602 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 16:59:13,603 - core.automation_engine - INFO - Executing event 9/27: keyboard
2025-06-03 16:59:14,313 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 16:59:14,966 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746798B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:59:15,114 - core.automation_engine - INFO - Executing event 10/27: keyboard
2025-06-03 16:59:15,791 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 16:59:16,592 - core.automation_engine - INFO - Executing event 11/27: keyboard
2025-06-03 16:59:17,293 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 16:59:18,294 - core.automation_engine - INFO - Executing event 12/27: input
2025-06-03 16:59:18,644 - core.automation_engine - ERROR - Error executing event 11: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 16:59:18,645 - core.automation_engine - INFO - Executing event 13/27: keyboard
2025-06-03 16:59:19,334 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: :focus
2025-06-03 16:59:20,335 - core.automation_engine - INFO - Executing event 14/27: keyboard
2025-06-03 16:59:23,118 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746797C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:24,344 - core.automation_engine - INFO - Sent key 'Enter' to element: :focus
2025-06-03 16:59:26,346 - core.automation_engine - INFO - Executing event 15/27: wait_for_element
2025-06-03 16:59:27,189 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746794F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:31,277 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:35,357 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(4) (visible: True)
2025-06-03 16:59:36,393 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-03 16:59:39,427 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:43,501 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:46,423 - core.automation_engine - ERROR - Error executing event 14: Element condition not met within 5.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-03 16:59:46,423 - core.automation_engine - INFO - Executing event 16/27: input
2025-06-03 16:59:47,619 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 16:59:51,714 - core.automation_engine - ERROR - Error executing event 27: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746787D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 16:59:51,714 - core.automation_engine - INFO - Executing event 29/36: input
2025-06-03 16:59:55,800 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746785F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 16:59:59,853 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:00:03,946 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:00:06,576 - core.automation_engine - ERROR - Error executing event 15: Input element not found: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-03 17:00:06,576 - core.automation_engine - INFO - Executing event 17/27: keyboard
2025-06-03 17:00:12,105 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:16,196 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A5D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:20,286 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A7B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:26,693 - core.automation_engine - ERROR - Error executing event 16: Element not found for keyboard input: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-03 17:00:26,694 - core.automation_engine - INFO - Executing event 18/27: keyboard
2025-06-03 17:00:28,441 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467AD50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:32,518 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:36,573 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:00:44,742 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374450E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:00:48,823 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451A90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:00:52,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:00:56,952 - core.automation_engine - ERROR - Error executing event 28: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744519A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:00:56,953 - core.automation_engine - INFO - Executing event 30/36: keyboard
2025-06-03 17:01:01,001 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451C70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:01:05,075 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374451D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:01:09,169 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:01:17,321 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744523F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:21,414 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452A80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:25,485 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:33,663 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:37,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374452E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:41,790 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:01:49,938 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744537A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:01:53,998 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374453F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:01:58,079 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293744536B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:02:02,169 - core.automation_engine - ERROR - Error executing event 29: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467AB70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:02:02,169 - core.automation_engine - INFO - Executing event 31/36: keyboard
2025-06-03 17:02:06,256 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467AC60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:02:10,316 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A8A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:02:14,379 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A6C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:02:22,510 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467A300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:26,589 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:30,650 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:38,766 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:42,830 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:46,922 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:02:55,035 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:02:59,063 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746794F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:03:03,089 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746797C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/source
2025-06-03 17:03:07,176 - core.automation_engine - ERROR - Error executing event 30: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746795E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:03:07,176 - core.automation_engine - INFO - Executing event 32/36: wait_for_element
2025-06-03 17:03:11,256 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746796D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:03:15,323 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746799A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:03:19,413 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467B020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:03:27,560 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374096F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:31,661 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374097A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:35,737 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374097890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:39,818 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(5) (visible: True)
2025-06-03 17:03:43,871 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293740975C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:47,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374097200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:52,050 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374096D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:03:56,129 - core.automation_engine - ERROR - Error executing event 31: HTTPConnectionPool(host='localhost', port=55712): Max retries exceeded with url: /session/9a6a452b2cf6f26e900ca691d86fb989/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374097020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:03:56,129 - core.automation_engine - INFO - Executing event 33/36: input
2025-06-03 17:04:00,211 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002937467B2F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:04:04,268 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:04:08,339 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:04:16,497 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374679220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:04:20,564 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:04:24,662 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:04:32,762 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029374678500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/element
2025-06-03 17:04:36,859 - main - INFO - Cleaning up resources
2025-06-03 17:04:40,909 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000293746789B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9a6a452b2cf6f26e900ca691d86fb989/execute/sync
2025-06-03 17:04:57,119 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 17:04:57,120 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 17:04:57,902 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 17:04:58,216 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 17:04:58,535 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 17:04:59,958 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 17:04:59,981 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 17:05:02,764 - main - INFO - Application initialized successfully
2025-06-03 17:05:02,765 - main - INFO - Starting complete Millware automation flow
2025-06-03 17:05:02,766 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 17:05:02,766 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 17:05:02,767 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 17:05:02,768 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 17:05:02,769 - main - INFO - Loaded flow with 27 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 17:05:02,769 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 17:05:02,769 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 17:05:02,770 - core.automation_engine - INFO - Executing event 1/38: navigate
2025-06-03 17:05:02,770 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 17:05:04,653 - core.automation_engine - INFO - Executing event 2/38: wait_for_element
2025-06-03 17:05:04,999 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 17:05:05,344 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 17:05:05,846 - core.automation_engine - INFO - Executing event 3/38: input
2025-06-03 17:05:06,538 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 17:05:07,039 - core.automation_engine - INFO - Executing event 4/38: input
2025-06-03 17:05:07,790 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 17:05:08,291 - core.automation_engine - INFO - Executing event 5/38: click
2025-06-03 17:05:11,749 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 17:05:12,250 - core.automation_engine - INFO - Executing event 6/38: wait
2025-06-03 17:05:12,251 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 17:05:14,253 - core.automation_engine - INFO - Executing event 7/38: popup_handler
2025-06-03 17:05:14,254 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 17:05:14,272 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 17:05:15,609 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 17:05:21,296 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 17:05:22,297 - core.automation_engine - INFO - Executing event 8/38: wait
2025-06-03 17:05:22,298 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:05:23,799 - core.automation_engine - INFO - Executing event 9/38: navigate
2025-06-03 17:05:23,800 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 17:05:26,288 - core.automation_engine - INFO - Executing event 10/38: wait
2025-06-03 17:05:26,289 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:05:27,790 - core.automation_engine - INFO - Executing event 11/38: wait
2025-06-03 17:05:27,791 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 17:05:31,293 - core.automation_engine - INFO - Executing event 12/38: wait_for_element
2025-06-03 17:05:31,654 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 17:05:31,993 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 17:05:32,494 - core.automation_engine - INFO - Executing event 13/38: input
2025-06-03 17:05:33,418 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E05D3]
	(No symbol) [0x007F2061]
	(No symbol) [0x007F1140]
	(No symbol) [0x007E7880]
	(No symbol) [0x007E5D81]
	(No symbol) [0x007E90CA]
	(No symbol) [0x007E9147]
	(No symbol) [0x0082963A]
	(No symbol) [0x008296C1]
	(No symbol) [0x008210F4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 17:05:33,419 - core.automation_engine - INFO - Executing event 14/38: click
2025-06-03 17:05:34,358 - core.automation_engine - INFO - Clicked element: body
2025-06-03 17:05:34,859 - core.automation_engine - INFO - Executing event 15/38: wait
2025-06-03 17:05:34,860 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 17:05:36,861 - core.automation_engine - INFO - Executing event 16/38: wait_for_element
2025-06-03 17:05:37,216 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 17:05:37,546 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 17:05:38,047 - core.automation_engine - INFO - Executing event 17/38: input
2025-06-03 17:05:38,846 - core.automation_engine - INFO - Input value 'SEPTIAN ADE PRATAMA' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:05:39,347 - core.automation_engine - INFO - Executing event 18/38: keyboard
2025-06-03 17:05:40,050 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:05:41,052 - core.automation_engine - INFO - Executing event 19/38: keyboard
2025-06-03 17:05:41,754 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:05:43,756 - core.automation_engine - INFO - Executing event 20/38: keyboard
2025-06-03 17:05:44,462 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:05:45,263 - core.automation_engine - INFO - Executing event 21/38: keyboard
2025-06-03 17:05:45,951 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:05:46,753 - core.automation_engine - INFO - Executing event 22/38: keyboard
2025-06-03 17:05:47,454 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:05:48,456 - core.automation_engine - INFO - Executing event 23/38: input
2025-06-03 17:05:48,811 - core.automation_engine - ERROR - Error executing event 22: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x009BFC03+61635]
	GetHandleVerifier [0x009BFC44+61700]
	(No symbol) [0x007E044E]
	(No symbol) [0x0081FFE4]
	(No symbol) [0x0084D29C]
	(No symbol) [0x0081E034]
	(No symbol) [0x0084D514]
	(No symbol) [0x0086E61B]
	(No symbol) [0x0084D096]
	(No symbol) [0x0081C840]
	(No symbol) [0x0081D6A4]
	GetHandleVerifier [0x00C44523+2701795]
	GetHandleVerifier [0x00C3FCA6+2683238]
	GetHandleVerifier [0x00C5A9EE+2793134]
	GetHandleVerifier [0x009D68C5+155013]
	GetHandleVerifier [0x009DCFAD+181357]
	GetHandleVerifier [0x009C7458+92440]
	GetHandleVerifier [0x009C7600+92864]
	GetHandleVerifier [0x009B1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 17:05:48,812 - core.automation_engine - INFO - Executing event 24/38: keyboard
2025-06-03 17:05:49,492 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: :focus
2025-06-03 17:05:50,494 - core.automation_engine - INFO - Executing event 25/38: keyboard
2025-06-03 17:05:54,475 - core.automation_engine - INFO - Sent key 'Enter' to element: :focus
2025-06-03 17:05:56,476 - core.automation_engine - INFO - Executing event 26/38: wait_for_element
2025-06-03 17:05:59,657 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-03 17:06:03,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BC4CB90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:07,796 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BC4D810>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:11,866 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB13360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:15,938 - core.automation_engine - ERROR - Error executing event 25: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB12EA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:06:15,938 - core.automation_engine - INFO - Executing event 27/38: input
2025-06-03 17:06:19,997 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BC2A9F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:06:24,098 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB3B790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:06:28,170 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB3B9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:06:36,311 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BC3AE50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:40,366 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:44,472 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:52,623 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD347D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:06:56,668 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD349B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:07:00,709 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:07:08,863 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:07:12,914 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:07:17,001 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:07:21,082 - core.automation_engine - ERROR - Error executing event 26: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD355E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:07:21,082 - core.automation_engine - INFO - Executing event 28/38: keyboard
2025-06-03 17:07:25,154 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD356D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:07:29,217 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD359A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:07:33,293 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:07:41,418 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:07:45,482 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:07:49,541 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD363F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:07:57,687 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:08:01,752 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36B70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:08:05,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:08:13,970 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:08:18,054 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:08:22,152 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB374D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:08:26,206 - core.automation_engine - ERROR - Error executing event 27: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB372F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:08:26,207 - core.automation_engine - INFO - Executing event 29/38: keyboard
2025-06-03 17:08:30,301 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:08:34,386 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD367B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:08:38,486 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD365D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:08:46,630 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:08:50,715 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:08:54,801 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:02,916 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD358B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:07,009 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD355E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:11,076 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD354F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:19,200 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:09:23,274 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:09:27,316 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:09:31,371 - core.automation_engine - ERROR - Error executing event 28: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD349B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:09:31,372 - core.automation_engine - INFO - Executing event 30/38: wait_for_element
2025-06-03 17:09:35,430 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:09:39,503 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD345F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:09:43,572 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:09:51,718 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:55,781 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:09:59,865 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:03,972 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(4) (visible: True)
2025-06-03 17:10:08,050 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD372F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:12,134 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD376B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:16,189 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:20,255 - core.automation_engine - ERROR - Error executing event 29: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:10:20,256 - core.automation_engine - INFO - Executing event 31/38: input
2025-06-03 17:10:24,313 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:10:28,402 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:10:32,485 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:10:40,640 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:44,709 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C7D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:48,762 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:10:56,878 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:11:00,962 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:11:05,038 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:11:13,191 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD372F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:11:17,260 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:11:21,376 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:11:25,494 - core.automation_engine - ERROR - Error executing event 30: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:11:25,495 - core.automation_engine - INFO - Executing event 32/38: keyboard
2025-06-03 17:11:29,612 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:11:33,700 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36C60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:11:37,777 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:11:45,983 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD346E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:11:50,086 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD349B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:11:54,180 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:02,348 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:06,449 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:10,526 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:18,727 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD358B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:12:22,830 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35C70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:12:26,924 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:12:31,023 - core.automation_engine - ERROR - Error executing event 31: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:12:31,023 - core.automation_engine - INFO - Executing event 33/38: keyboard
2025-06-03 17:12:35,140 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:12:39,219 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:12:43,297 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD365D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:12:51,482 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD368A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:55,572 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB36990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:12:59,672 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB374D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:13:07,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:13:11,952 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:13:16,025 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:13:24,213 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB375C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:13:28,312 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7CD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:13:32,426 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7CF50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:13:36,523 - core.automation_engine - ERROR - Error executing event 32: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7D130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:13:36,525 - core.automation_engine - INFO - Executing event 34/38: wait_for_element
2025-06-03 17:13:40,609 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7D220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:13:44,713 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7D4F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:13:48,795 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:13:56,972 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB37E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:01,070 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB374D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:05,167 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BB36990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:09,244 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(5) (visible: True)
2025-06-03 17:14:13,340 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD368A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:17,424 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD365D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:21,508 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:25,597 - core.automation_engine - ERROR - Error executing event 33: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:14:25,598 - core.automation_engine - INFO - Executing event 35/38: input
2025-06-03 17:14:29,691 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:14:33,790 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:14:37,868 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:14:46,036 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD357C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:50,109 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:14:54,195 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:02,378 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:06,449 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:10,510 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:18,644 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:15:22,690 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:15:26,748 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:15:30,824 - core.automation_engine - ERROR - Error executing event 34: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:15:30,825 - core.automation_engine - INFO - Executing event 36/38: keyboard
2025-06-03 17:15:34,900 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:15:38,977 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:15:43,052 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:15:51,193 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD375C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:55,269 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:15:59,357 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:16:07,482 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7CF50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:16:11,583 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7CD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:16:15,689 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7C140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:16:23,906 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:16:28,003 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7CB90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:16:32,083 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7D6D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:16:36,190 - core.automation_engine - ERROR - Error executing event 35: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD7D8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:16:36,191 - core.automation_engine - INFO - Executing event 37/38: keyboard
2025-06-03 17:16:40,279 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:16:44,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD37980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:16:48,425 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD375C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:16:56,542 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD374D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:00,630 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:04,714 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD36D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:12,854 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:16,909 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34AA0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:21,016 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD348C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/element
2025-06-03 17:17:29,118 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD347D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:17:33,203 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD349B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:17:37,260 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/source
2025-06-03 17:17:41,325 - core.automation_engine - ERROR - Error executing event 36: HTTPConnectionPool(host='localhost', port=58098): Max retries exceeded with url: /session/bfd25e5a6fdacba4881d1639cac25185/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:17:41,325 - core.automation_engine - INFO - Executing event 38/38: wait
2025-06-03 17:17:41,325 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:17:42,827 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 17:17:42,827 - main - INFO - Complete Millware flow completed successfully
2025-06-03 17:17:46,923 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD34F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:17:50,968 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD35220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:17:55,044 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002012BD357C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bfd25e5a6fdacba4881d1639cac25185/execute/sync
2025-06-03 17:23:25,248 - main - INFO - Cleaning up resources
2025-06-03 17:23:33,820 - main - INFO - Initializing Selenium AutoFill Application
2025-06-03 17:23:33,821 - WDM - INFO - ====== WebDriver manager ======
2025-06-03 17:23:35,208 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 17:23:35,530 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-03 17:23:35,858 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-03 17:23:37,442 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-03 17:23:37,466 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-03 17:23:39,933 - main - INFO - Application initialized successfully
2025-06-03 17:23:39,934 - main - INFO - Starting complete Millware automation flow
2025-06-03 17:23:39,967 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 17:23:39,968 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-03 17:23:39,968 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 17:23:39,987 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-03 17:23:39,988 - main - INFO - Loaded flow with 27 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-03 17:23:39,988 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-03 17:23:39,988 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-03 17:23:39,988 - core.automation_engine - INFO - Executing event 1/38: navigate
2025-06-03 17:23:39,988 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-03 17:23:41,781 - core.automation_engine - INFO - Executing event 2/38: wait_for_element
2025-06-03 17:23:42,124 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-03 17:23:42,481 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-03 17:23:42,982 - core.automation_engine - INFO - Executing event 3/38: input
2025-06-03 17:23:43,724 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-03 17:23:44,225 - core.automation_engine - INFO - Executing event 4/38: input
2025-06-03 17:23:44,992 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-03 17:23:45,493 - core.automation_engine - INFO - Executing event 5/38: click
2025-06-03 17:23:48,886 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-03 17:23:49,388 - core.automation_engine - INFO - Executing event 6/38: wait
2025-06-03 17:23:49,388 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 17:23:51,390 - core.automation_engine - INFO - Executing event 7/38: popup_handler
2025-06-03 17:23:51,390 - core.automation_engine - INFO - Handling popup dialogs
2025-06-03 17:23:51,412 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-03 17:23:52,755 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-03 17:23:59,634 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-03 17:24:00,636 - core.automation_engine - INFO - Executing event 8/38: wait
2025-06-03 17:24:00,637 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:24:02,138 - core.automation_engine - INFO - Executing event 9/38: navigate
2025-06-03 17:24:02,138 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-03 17:24:04,504 - core.automation_engine - INFO - Executing event 10/38: wait
2025-06-03 17:24:04,505 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:24:06,006 - core.automation_engine - INFO - Executing event 11/38: wait
2025-06-03 17:24:06,007 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-03 17:24:09,509 - core.automation_engine - INFO - Executing event 12/38: wait_for_element
2025-06-03 17:24:09,879 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-03 17:24:10,206 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-03 17:24:10,707 - core.automation_engine - INFO - Executing event 13/38: input
2025-06-03 17:24:11,492 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x003FFC03+61635]
	GetHandleVerifier [0x003FFC44+61700]
	(No symbol) [0x002205D3]
	(No symbol) [0x00232061]
	(No symbol) [0x00231140]
	(No symbol) [0x00227880]
	(No symbol) [0x00225D81]
	(No symbol) [0x002290CA]
	(No symbol) [0x00229147]
	(No symbol) [0x0026963A]
	(No symbol) [0x002696C1]
	(No symbol) [0x002610F4]
	(No symbol) [0x0028D29C]
	(No symbol) [0x0025E034]
	(No symbol) [0x0028D514]
	(No symbol) [0x002AE61B]
	(No symbol) [0x0028D096]
	(No symbol) [0x0025C840]
	(No symbol) [0x0025D6A4]
	GetHandleVerifier [0x00684523+2701795]
	GetHandleVerifier [0x0067FCA6+2683238]
	GetHandleVerifier [0x0069A9EE+2793134]
	GetHandleVerifier [0x004168C5+155013]
	GetHandleVerifier [0x0041CFAD+181357]
	GetHandleVerifier [0x00407458+92440]
	GetHandleVerifier [0x00407600+92864]
	GetHandleVerifier [0x003F1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 17:24:11,492 - core.automation_engine - INFO - Executing event 14/38: click
2025-06-03 17:24:12,432 - core.automation_engine - INFO - Clicked element: body
2025-06-03 17:24:12,932 - core.automation_engine - INFO - Executing event 15/38: wait
2025-06-03 17:24:12,933 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-03 17:24:14,934 - core.automation_engine - INFO - Executing event 16/38: wait_for_element
2025-06-03 17:24:15,261 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox (visible: True)
2025-06-03 17:24:15,592 - core.automation_engine - INFO - Element condition met: input.ui-autocomplete-input.CBOBox
2025-06-03 17:24:16,093 - core.automation_engine - INFO - Executing event 17/38: input
2025-06-03 17:24:16,835 - core.automation_engine - INFO - Input value 'SEPTIAN ADE PRATAMA' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:24:17,336 - core.automation_engine - INFO - Executing event 18/38: keyboard
2025-06-03 17:24:18,002 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:24:19,004 - core.automation_engine - INFO - Executing event 19/38: keyboard
2025-06-03 17:24:19,691 - core.automation_engine - INFO - Sent key 'Enter' to element: input.ui-autocomplete-input.CBOBox
2025-06-03 17:24:21,693 - core.automation_engine - INFO - Executing event 20/38: keyboard
2025-06-03 17:24:22,389 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:24:23,191 - core.automation_engine - INFO - Executing event 21/38: keyboard
2025-06-03 17:24:23,894 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:24:24,695 - core.automation_engine - INFO - Executing event 22/38: keyboard
2025-06-03 17:24:25,366 - core.automation_engine - INFO - Sent key 'Tab' to element: body
2025-06-03 17:24:26,368 - core.automation_engine - INFO - Executing event 23/38: input
2025-06-03 17:24:26,713 - core.automation_engine - ERROR - Error executing event 22: Message: invalid element state
  (Session info: chrome=136.0.7103.116)
Stacktrace:
	GetHandleVerifier [0x003FFC03+61635]
	GetHandleVerifier [0x003FFC44+61700]
	(No symbol) [0x0022044E]
	(No symbol) [0x0025FFE4]
	(No symbol) [0x0028D29C]
	(No symbol) [0x0025E034]
	(No symbol) [0x0028D514]
	(No symbol) [0x002AE61B]
	(No symbol) [0x0028D096]
	(No symbol) [0x0025C840]
	(No symbol) [0x0025D6A4]
	GetHandleVerifier [0x00684523+2701795]
	GetHandleVerifier [0x0067FCA6+2683238]
	GetHandleVerifier [0x0069A9EE+2793134]
	GetHandleVerifier [0x004168C5+155013]
	GetHandleVerifier [0x0041CFAD+181357]
	GetHandleVerifier [0x00407458+92440]
	GetHandleVerifier [0x00407600+92864]
	GetHandleVerifier [0x003F1FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-03 17:24:26,713 - core.automation_engine - INFO - Executing event 24/38: keyboard
2025-06-03 17:24:27,375 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: :focus
2025-06-03 17:24:28,376 - core.automation_engine - INFO - Executing event 25/38: keyboard
2025-06-03 17:24:32,448 - core.automation_engine - INFO - Sent key 'Enter' to element: :focus
2025-06-03 17:24:34,449 - core.automation_engine - INFO - Executing event 26/38: wait_for_element
2025-06-03 17:24:44,482 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-03 17:24:54,531 - core.automation_engine - ERROR - Error executing event 25: Element condition not met within 5.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-03 17:24:54,531 - core.automation_engine - INFO - Executing event 27/38: input
2025-06-03 17:25:16,865 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254104ACB90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:25:20,921 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254104AD590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:25:24,990 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410372FD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:25:29,041 - core.automation_engine - ERROR - Error executing event 26: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410373BB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:25:29,042 - core.automation_engine - INFO - Executing event 28/38: keyboard
2025-06-03 17:25:33,089 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002541048A9F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:25:37,170 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002541039B8A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:25:41,267 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002541039BAC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:25:49,405 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002541049AE50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:25:53,449 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:25:57,531 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:26:05,674 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:26:09,738 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A49B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:26:13,803 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:26:21,960 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:26:26,027 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:26:30,095 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:26:34,155 - core.automation_engine - ERROR - Error executing event 27: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A55E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:26:34,155 - core.automation_engine - INFO - Executing event 29/38: keyboard
2025-06-03 17:26:38,210 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A56D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:26:42,264 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A59A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:26:46,305 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:26:54,449 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:26:58,501 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:27:02,560 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A63F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:27:10,694 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:27:14,745 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6B70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:27:18,821 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:27:26,970 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:27:31,019 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103976B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:27:35,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103972F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:27:39,168 - core.automation_engine - ERROR - Error executing event 28: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103973E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:27:39,179 - core.automation_engine - INFO - Executing event 30/38: wait_for_element
2025-06-03 17:27:43,239 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410396990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:27:47,326 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A67B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:27:51,400 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A65D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:27:59,530 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:03,586 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:07,694 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:11,755 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(4) (visible: True)
2025-06-03 17:28:15,837 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A59A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:19,890 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A57C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:23,977 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5400>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:28,041 - core.automation_engine - ERROR - Error executing event 29: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:28:28,042 - core.automation_engine - INFO - Executing event 31/38: input
2025-06-03 17:28:32,098 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:28:36,189 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:28:40,257 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:28:48,413 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A49B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:52,507 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A45F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:28:56,568 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:29:04,690 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:29:08,763 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:29:12,809 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:29:20,974 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A73E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:29:25,051 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A77A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:29:29,108 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:29:33,170 - core.automation_engine - ERROR - Error executing event 30: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:29:33,170 - core.automation_engine - INFO - Executing event 32/38: keyboard
2025-06-03 17:29:37,241 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7C50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:29:41,318 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:29:45,378 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:29:53,496 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:29:57,569 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E07D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:30:01,603 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E09B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:30:09,735 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:30:13,814 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:30:17,872 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A77A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:30:26,031 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4050>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:30:30,109 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A72F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:30:34,182 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:30:38,218 - core.automation_engine - ERROR - Error executing event 31: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:30:38,218 - core.automation_engine - INFO - Executing event 33/38: keyboard
2025-06-03 17:30:42,300 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:30:46,373 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6C60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:30:50,451 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:30:58,579 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A46E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:02,642 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A47D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:06,717 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:14,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:18,978 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:23,027 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A54F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:31:31,136 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A59A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:31:35,211 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5C70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:31:39,294 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5B80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:31:43,383 - core.automation_engine - ERROR - Error executing event 32: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:31:43,384 - core.automation_engine - INFO - Executing event 34/38: wait_for_element
2025-06-03 17:31:47,458 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:31:51,544 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5E50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:31:55,611 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A65D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:32:03,767 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A68A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:07,835 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:11,930 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103972F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:16,009 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(5) (visible: True)
2025-06-03 17:32:20,092 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103975C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:24,162 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:28,267 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:32,336 - core.automation_engine - ERROR - Error executing event 33: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E05F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:32:32,338 - core.automation_engine - INFO - Executing event 35/38: input
2025-06-03 17:32:36,406 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:32:40,467 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:32:44,551 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:32:52,685 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E1130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:32:56,754 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E14F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:33:00,836 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397E30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:33:08,943 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103976B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:33:13,058 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103974D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:33:17,119 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254103973E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:33:25,243 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025410397A70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:33:29,337 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A63F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:33:33,405 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A64E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:33:37,475 - core.automation_engine - ERROR - Error executing event 34: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6300>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:33:37,476 - core.automation_engine - INFO - Executing event 36/38: keyboard
2025-06-03 17:33:41,537 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6120>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:33:45,595 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:33:49,677 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5D60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:33:57,806 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A58B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:01,886 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A54F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:05,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5220>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:14,107 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:18,189 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:22,247 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4C80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:34:30,380 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A49B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:34:34,436 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:34:38,488 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4320>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:34:42,574 - core.automation_engine - ERROR - Error executing event 35: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:34:42,574 - core.automation_engine - INFO - Executing event 37/38: keyboard
2025-06-03 17:34:46,627 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:34:50,690 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:34:54,769 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7110>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:35:02,886 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A74D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:06,960 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A77A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:11,028 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:19,197 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E1040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:23,270 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:27,337 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0D70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/element
2025-06-03 17:35:35,503 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7B60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:35:39,579 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E0B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:35:43,638 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E16D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/source
2025-06-03 17:35:47,714 - core.automation_engine - ERROR - Error executing event 36: HTTPConnectionPool(host='localhost', port=60309): Max retries exceeded with url: /session/edb37c5c08f8651eeef74bbd38663583/source (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105E18B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-03 17:35:47,715 - core.automation_engine - INFO - Executing event 38/38: wait
2025-06-03 17:35:47,716 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-03 17:35:49,217 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-03 17:35:49,217 - main - INFO - Complete Millware flow completed successfully
2025-06-03 17:35:53,314 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A7D40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:35:57,375 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A77A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-03 17:36:01,415 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A74D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:29,248 - main - INFO - Cleaning up resources
2025-06-04 07:42:33,316 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A76B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:37,379 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:41,471 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6E40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:49,608 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:53,690 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A6C60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:42:57,746 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4500>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:43:05,887 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:43:09,960 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4B90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:43:13,998 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A5040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583/execute/sync
2025-06-04 07:43:22,183 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A4F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583
2025-06-04 07:43:26,283 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A54F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583
2025-06-04 07:43:30,372 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000254105A58B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/edb37c5c08f8651eeef74bbd38663583
2025-06-04 07:43:34,441 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-04 07:43:34,442 - main - INFO - Cleanup completed
2025-06-04 07:58:00,182 - main - INFO - Initializing Selenium AutoFill Application
2025-06-04 07:58:00,182 - WDM - INFO - ====== WebDriver manager ======
2025-06-04 07:58:01,435 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 07:58:02,357 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 07:58:03,236 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 07:58:05,179 - WDM - INFO - WebDriver version 136.0.7103.113 selected
2025-06-04 07:58:05,187 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-04 07:58:05,187 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.113/win32/chromedriver-win32.zip
2025-06-04 07:58:06,275 - WDM - INFO - Driver downloading response is 200
2025-06-04 07:58:07,946 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 07:58:09,224 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113]
2025-06-04 07:58:10,989 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-04 07:58:11,014 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-04 07:58:13,409 - main - INFO - Application initialized successfully
2025-06-04 08:04:29,304 - main - INFO - 📋 Starting post-login automation flow
2025-06-04 08:04:29,325 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-04 08:04:29,327 - main - INFO - Loaded flow with 26 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-04 08:04:29,327 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-04 08:04:29,328 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-04 08:04:29,328 - core.automation_engine - INFO - Executing event 1/26: wait_for_element
2025-06-04 08:04:29,387 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-04 08:04:29,388 - core.automation_engine - ERROR - Error executing event 0: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:29,389 - core.automation_engine - INFO - Executing event 2/26: input
2025-06-04 08:04:29,392 - core.automation_engine - ERROR - Error executing event 1: Input element not found: #MainContent_txtTrxDate
2025-06-04 08:04:29,392 - core.automation_engine - INFO - Executing event 3/26: click
2025-06-04 08:04:29,396 - core.automation_engine - ERROR - Error executing event 2: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:29,398 - core.automation_engine - INFO - Executing event 4/26: wait
2025-06-04 08:04:29,398 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-04 08:04:31,400 - core.automation_engine - INFO - Executing event 5/26: wait_for_element
2025-06-04 08:04:31,403 - core.automation_engine - INFO - Waiting for element: #MainContent_ddlEmployee + input.ui-autocomplete-input (visible: True)
2025-06-04 08:04:31,405 - core.automation_engine - ERROR - Error executing event 4: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:31,405 - core.automation_engine - INFO - Executing event 6/26: input
2025-06-04 08:04:31,411 - core.automation_engine - ERROR - Error executing event 5: Input element not found: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:04:31,412 - core.automation_engine - INFO - Executing event 7/26: keyboard
2025-06-04 08:04:31,419 - core.automation_engine - ERROR - Error executing event 6: Element not found for keyboard input: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:04:31,419 - core.automation_engine - INFO - Executing event 8/26: keyboard
2025-06-04 08:04:31,424 - core.automation_engine - ERROR - Error executing event 7: Element not found for keyboard input: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:04:31,425 - core.automation_engine - INFO - Executing event 9/26: wait_for_element
2025-06-04 08:04:31,429 - core.automation_engine - INFO - Waiting for element: #MainContent_ddlTaskCode + input.ui-autocomplete-input (visible: True)
2025-06-04 08:04:31,431 - core.automation_engine - ERROR - Error executing event 8: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:31,432 - core.automation_engine - INFO - Executing event 10/26: input
2025-06-04 08:04:31,435 - core.automation_engine - ERROR - Error executing event 9: Input element not found: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:04:31,436 - core.automation_engine - INFO - Executing event 11/26: keyboard
2025-06-04 08:04:31,439 - core.automation_engine - ERROR - Error executing event 10: Element not found for keyboard input: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:04:31,440 - core.automation_engine - INFO - Executing event 12/26: keyboard
2025-06-04 08:04:31,443 - core.automation_engine - ERROR - Error executing event 11: Element not found for keyboard input: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:04:31,443 - core.automation_engine - INFO - Executing event 13/26: wait
2025-06-04 08:04:31,444 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-04 08:04:33,455 - core.automation_engine - INFO - Executing event 14/26: wait_for_element
2025-06-04 08:04:33,460 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-04 08:04:33,462 - core.automation_engine - ERROR - Error executing event 13: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,463 - core.automation_engine - INFO - Executing event 15/26: input
2025-06-04 08:04:33,476 - core.automation_engine - ERROR - Error executing event 14: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,477 - core.automation_engine - INFO - Executing event 16/26: keyboard
2025-06-04 08:04:33,489 - core.automation_engine - ERROR - Error executing event 15: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,490 - core.automation_engine - INFO - Executing event 17/26: keyboard
2025-06-04 08:04:33,501 - core.automation_engine - ERROR - Error executing event 16: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,502 - core.automation_engine - INFO - Executing event 18/26: wait_for_element
2025-06-04 08:04:33,507 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(4) (visible: True)
2025-06-04 08:04:33,510 - core.automation_engine - ERROR - Error executing event 17: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,510 - core.automation_engine - INFO - Executing event 19/26: input
2025-06-04 08:04:33,518 - core.automation_engine - ERROR - Error executing event 18: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,518 - core.automation_engine - INFO - Executing event 20/26: keyboard
2025-06-04 08:04:33,525 - core.automation_engine - ERROR - Error executing event 19: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,526 - core.automation_engine - INFO - Executing event 21/26: keyboard
2025-06-04 08:04:33,534 - core.automation_engine - ERROR - Error executing event 20: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,535 - core.automation_engine - INFO - Executing event 22/26: wait_for_element
2025-06-04 08:04:33,539 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(5) (visible: True)
2025-06-04 08:04:33,541 - core.automation_engine - ERROR - Error executing event 21: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,543 - core.automation_engine - INFO - Executing event 23/26: input
2025-06-04 08:04:33,550 - core.automation_engine - ERROR - Error executing event 22: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,551 - core.automation_engine - INFO - Executing event 24/26: keyboard
2025-06-04 08:04:33,558 - core.automation_engine - ERROR - Error executing event 23: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,559 - core.automation_engine - INFO - Executing event 25/26: keyboard
2025-06-04 08:04:33,564 - core.automation_engine - ERROR - Error executing event 24: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A044E]
	(No symbol) [0x008DBB08]
	(No symbol) [0x0090D156]
	(No symbol) [0x00908C22]
	(No symbol) [0x009081B6]
	(No symbol) [0x008736C5]
	(No symbol) [0x00873C1E]
	(No symbol) [0x008740AD]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	(No symbol) [0x00873390]
	(No symbol) [0x00872B9D]
	GetHandleVerifier [0x00DF877C+3701820]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:04:33,565 - core.automation_engine - INFO - Executing event 26/26: wait
2025-06-04 08:04:33,565 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-04 08:04:35,067 - core.automation_engine - INFO - ✅ Automation flow completed successfully
2025-06-04 08:04:35,070 - main - INFO - ✅ Post-login flow completed successfully
2025-06-04 08:04:48,348 - main - INFO - Cleaning up resources
2025-06-04 08:04:52,405 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A24160980>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:04:56,478 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A240B8CD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:00,545 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A240B9310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:08,674 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A21F2AD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:12,719 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A240A6D50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:16,795 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A21F53240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:24,945 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A2409EA50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:29,022 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A2409FE50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:33,105 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A21F4BF20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8/execute/sync
2025-06-04 08:05:41,268 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A24184230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8
2025-06-04 08:05:45,350 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A241845F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8
2025-06-04 08:05:49,441 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021A241847D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f6077f43e05f3e730ac2a4c8f879e3d8
2025-06-04 08:05:53,504 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-04 08:05:53,504 - main - INFO - Cleanup completed
2025-06-04 08:08:50,082 - main - INFO - Initializing Selenium AutoFill Application
2025-06-04 08:08:50,083 - WDM - INFO - ====== WebDriver manager ======
2025-06-04 08:08:50,913 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 08:08:51,485 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-04 08:08:51,817 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-06-04 08:08:53,270 - core.browser_manager - INFO - Chrome WebDriver created successfully
2025-06-04 08:08:53,304 - main - INFO - Opening default Millware URL: http://millwarep3.rebinmas.com:8003/
2025-06-04 08:08:56,771 - main - INFO - Application initialized successfully
2025-06-04 08:08:56,773 - main - INFO - Starting complete Millware automation flow
2025-06-04 08:08:56,775 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-04 08:08:56,776 - main - INFO - Loaded flow with 10 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\pre_login_flow.json
2025-06-04 08:08:56,776 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-04 08:08:56,778 - main - INFO - Loaded form data configuration from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\config\form_data.json
2025-06-04 08:08:56,779 - main - INFO - Loaded flow with 26 events from D:\Venus AutoFill Browser\Ekstensi_auto_fill_login\Selenium Auto Fill\flows\post_login_flow.json
2025-06-04 08:08:56,780 - main - INFO - Applied form data: Date=03/06/2025, Employee=SEPTIAN ADE PRATAMA
2025-06-04 08:08:56,781 - core.automation_engine - INFO - 🚀 Starting automation flow execution
2025-06-04 08:08:56,782 - core.automation_engine - INFO - Executing event 1/37: navigate
2025-06-04 08:08:56,782 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/
2025-06-04 08:08:58,783 - core.automation_engine - INFO - Executing event 2/37: wait_for_element
2025-06-04 08:08:59,134 - core.automation_engine - INFO - Waiting for element: #txtUsername (visible: True)
2025-06-04 08:08:59,536 - core.automation_engine - INFO - Element condition met: #txtUsername
2025-06-04 08:09:00,037 - core.automation_engine - INFO - Executing event 3/37: input
2025-06-04 08:09:00,781 - core.automation_engine - INFO - Input value 'adm075' to element: #txtUsername
2025-06-04 08:09:01,281 - core.automation_engine - INFO - Executing event 4/37: input
2025-06-04 08:09:02,029 - core.automation_engine - INFO - Input value 'adm075' to element: #txtPassword
2025-06-04 08:09:02,529 - core.automation_engine - INFO - Executing event 5/37: click
2025-06-04 08:09:06,033 - core.automation_engine - INFO - Clicked element: #btnLogin
2025-06-04 08:09:06,534 - core.automation_engine - INFO - Executing event 6/37: wait
2025-06-04 08:09:06,535 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-04 08:09:08,537 - core.automation_engine - INFO - Executing event 7/37: popup_handler
2025-06-04 08:09:08,538 - core.automation_engine - INFO - Handling popup dialogs
2025-06-04 08:09:08,574 - core.automation_engine - INFO - Popup found with selector: #MainContent_mpopLocation_backgroundElement
2025-06-04 08:09:09,920 - core.automation_engine - INFO - OK button found with selector: #MainContent_btnOkay
2025-06-04 08:09:16,396 - core.automation_engine - INFO - Popup dismissed by clicking OK button
2025-06-04 08:09:17,397 - core.automation_engine - INFO - Executing event 8/37: wait
2025-06-04 08:09:17,398 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-04 08:09:18,899 - core.automation_engine - INFO - Executing event 9/37: navigate
2025-06-04 08:09:18,900 - core.automation_engine - INFO - Navigating to: http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx
2025-06-04 08:09:21,228 - core.automation_engine - INFO - Executing event 10/37: wait
2025-06-04 08:09:21,229 - core.automation_engine - INFO - Waiting for 1.0 seconds
2025-06-04 08:09:22,730 - core.automation_engine - INFO - Executing event 11/37: wait
2025-06-04 08:09:22,731 - core.automation_engine - INFO - Waiting for 3.0 seconds
2025-06-04 08:09:26,233 - core.automation_engine - INFO - Executing event 12/37: wait_for_element
2025-06-04 08:09:26,580 - core.automation_engine - INFO - Waiting for element: #MainContent_txtTrxDate (visible: True)
2025-06-04 08:09:26,915 - core.automation_engine - INFO - Element condition met: #MainContent_txtTrxDate
2025-06-04 08:09:27,416 - core.automation_engine - INFO - Executing event 13/37: input
2025-06-04 08:09:28,356 - core.automation_engine - ERROR - Error executing event 12: Message: stale element reference: stale element not found
  (Session info: chrome=136.0.7103.116); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00A7FC03+61635]
	GetHandleVerifier [0x00A7FC44+61700]
	(No symbol) [0x008A05D3]
	(No symbol) [0x008B2061]
	(No symbol) [0x008B1140]
	(No symbol) [0x008A7880]
	(No symbol) [0x008A5D81]
	(No symbol) [0x008A90CA]
	(No symbol) [0x008A9147]
	(No symbol) [0x008E963A]
	(No symbol) [0x008E96C1]
	(No symbol) [0x008E10F4]
	(No symbol) [0x0090D29C]
	(No symbol) [0x008DE034]
	(No symbol) [0x0090D514]
	(No symbol) [0x0092E61B]
	(No symbol) [0x0090D096]
	(No symbol) [0x008DC840]
	(No symbol) [0x008DD6A4]
	GetHandleVerifier [0x00D04523+2701795]
	GetHandleVerifier [0x00CFFCA6+2683238]
	GetHandleVerifier [0x00D1A9EE+2793134]
	GetHandleVerifier [0x00A968C5+155013]
	GetHandleVerifier [0x00A9CFAD+181357]
	GetHandleVerifier [0x00A87458+92440]
	GetHandleVerifier [0x00A87600+92864]
	GetHandleVerifier [0x00A71FF0+5296]
	BaseThreadInitThunk [0x76115D49+25]
	RtlInitializeExceptionChain [0x7711D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7711CFC1+561]

2025-06-04 08:09:28,357 - core.automation_engine - INFO - Executing event 14/37: click
2025-06-04 08:09:29,311 - core.automation_engine - INFO - Clicked element: body
2025-06-04 08:09:29,812 - core.automation_engine - INFO - Executing event 15/37: wait
2025-06-04 08:09:29,813 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-04 08:09:31,823 - core.automation_engine - INFO - Executing event 16/37: wait_for_element
2025-06-04 08:09:32,157 - core.automation_engine - INFO - Waiting for element: #MainContent_ddlEmployee + input.ui-autocomplete-input (visible: True)
2025-06-04 08:09:32,484 - core.automation_engine - INFO - Element condition met: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:09:32,985 - core.automation_engine - INFO - Executing event 17/37: input
2025-06-04 08:09:33,794 - core.automation_engine - INFO - Input value 'SEPTIAN ADE PRATAMA' to element: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:09:34,295 - core.automation_engine - INFO - Executing event 18/37: keyboard
2025-06-04 08:09:34,998 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:09:36,000 - core.automation_engine - INFO - Executing event 19/37: keyboard
2025-06-04 08:09:36,711 - core.automation_engine - INFO - Sent key 'Enter' to element: #MainContent_ddlEmployee + input.ui-autocomplete-input
2025-06-04 08:09:38,712 - core.automation_engine - INFO - Executing event 20/37: wait_for_element
2025-06-04 08:09:39,058 - core.automation_engine - INFO - Waiting for element: #MainContent_ddlTaskCode + input.ui-autocomplete-input (visible: True)
2025-06-04 08:09:39,394 - core.automation_engine - INFO - Element condition met: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:09:39,895 - core.automation_engine - INFO - Executing event 21/37: input
2025-06-04 08:09:40,745 - core.automation_engine - INFO - Input value '(OC7240) LABORATORY ANALYSIS' to element: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:09:41,246 - core.automation_engine - INFO - Executing event 22/37: keyboard
2025-06-04 08:09:41,918 - core.automation_engine - INFO - Sent key 'ArrowDown' to element: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:09:42,920 - core.automation_engine - INFO - Executing event 23/37: keyboard
2025-06-04 08:09:43,642 - core.automation_engine - INFO - Sent key 'Enter' to element: #MainContent_ddlTaskCode + input.ui-autocomplete-input
2025-06-04 08:09:45,644 - core.automation_engine - INFO - Executing event 24/37: wait
2025-06-04 08:09:45,645 - core.automation_engine - INFO - Waiting for 1.5 seconds
2025-06-04 08:09:47,646 - core.automation_engine - INFO - Executing event 25/37: wait_for_element
2025-06-04 08:09:57,746 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(3) (visible: True)
2025-06-04 08:10:07,786 - core.automation_engine - ERROR - Error executing event 24: Element condition not met within 5.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-04 08:10:07,786 - core.automation_engine - INFO - Executing event 26/37: input
2025-06-04 08:10:27,942 - core.automation_engine - ERROR - Error executing event 25: Input element not found: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-04 08:10:27,943 - core.automation_engine - INFO - Executing event 27/37: keyboard
2025-06-04 08:10:48,169 - core.automation_engine - ERROR - Error executing event 26: Element not found for keyboard input: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-04 08:10:48,170 - core.automation_engine - INFO - Executing event 28/37: keyboard
2025-06-04 08:11:08,323 - core.automation_engine - ERROR - Error executing event 27: Element not found for keyboard input: input.ui-autocomplete-input.CBOBox:nth-of-type(3)
2025-06-04 08:11:08,324 - core.automation_engine - INFO - Executing event 29/37: wait_for_element
2025-06-04 08:11:18,341 - core.automation_engine - INFO - Waiting for element: input.ui-autocomplete-input.CBOBox:nth-of-type(4) (visible: True)
2025-06-04 08:11:28,351 - core.automation_engine - ERROR - Error executing event 28: Element condition not met within 3.0s: input.ui-autocomplete-input.CBOBox:nth-of-type(4)
2025-06-04 08:11:28,352 - core.automation_engine - INFO - Executing event 30/37: input
2025-06-04 08:11:48,496 - core.automation_engine - ERROR - Error executing event 29: Input element not found: input.ui-autocomplete-input.CBOBox:nth-of-type(4)
2025-06-04 08:11:48,497 - core.automation_engine - INFO - Executing event 31/37: keyboard
2025-06-04 08:12:08,670 - core.automation_engine - ERROR - Error executing event 30: Element not found for keyboard input: input.ui-autocomplete-input.CBOBox:nth-of-type(4)
2025-06-04 08:12:08,670 - core.automation_engine - INFO - Executing event 32/37: keyboard
2025-06-04 08:12:23,934 - main - INFO - Cleaning up resources
2025-06-04 08:12:27,965 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA2AD1D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:12:32,027 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA167BB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:12:36,102 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA167CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:12:44,228 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA197F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:12:48,264 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA95C160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:12:52,358 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA942F50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:13:00,508 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57E5D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:13:04,571 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57E990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:13:08,637 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57EB70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1/execute/sync
2025-06-04 08:13:16,760 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57EE40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1
2025-06-04 08:13:20,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57F200>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1
2025-06-04 08:13:24,870 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F8BA57F3E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/bb861b63dbdc99569624425db02a07b1
2025-06-04 08:13:28,940 - core.browser_manager - INFO - Chrome WebDriver quit successfully
2025-06-04 08:13:28,941 - main - INFO - Cleanup completed
