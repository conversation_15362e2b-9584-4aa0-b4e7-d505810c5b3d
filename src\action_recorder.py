"""
Action Recorder - Record user actions for automation flow creation
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime


class ActionRecorder:
    """Records user actions to create automation flows"""
    
    def __init__(self, driver):
        self.driver = driver
        self.recording = False
        self.session_name = None
        self.recorded_actions = []
        self.start_time = None
        self.logger = logging.getLogger(__name__)
    
    def start_recording(self, session_name: str = None):
        """Start recording user actions"""
        if self.recording:
            self.logger.warning("Recording already in progress")
            return
        
        self.recording = True
        self.session_name = session_name or f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.recorded_actions = []
        self.start_time = time.time()
        
        self.logger.info(f"Started recording session: {self.session_name}")
    
    def stop_recording(self) -> Optional[Dict[str, Any]]:
        """Stop recording and return recorded data"""
        if not self.recording:
            self.logger.warning("No recording in progress")
            return None
        
        self.recording = False
        end_time = time.time()
        duration = end_time - self.start_time
        
        recording_data = {
            'session_name': self.session_name,
            'duration': duration,
            'total_actions': len(self.recorded_actions),
            'actions': self.recorded_actions,
            'created_at': datetime.now().isoformat()
        }
        
        # Save recording to file
        self._save_recording(recording_data)
        
        self.logger.info(f"Stopped recording session: {self.session_name}")
        return recording_data
    
    def add_action(self, action_type: str, details: Dict[str, Any]):
        """Add an action to the current recording"""
        if not self.recording:
            return
        
        action = {
            'timestamp': time.time() - self.start_time,
            'type': action_type,
            'details': details
        }
        
        self.recorded_actions.append(action)
        self.logger.debug(f"Recorded action: {action_type}")
    
    def _save_recording(self, recording_data: Dict[str, Any]):
        """Save recording data to file"""
        try:
            recordings_dir = Path("recordings")
            recordings_dir.mkdir(exist_ok=True)
            
            filename = f"{self.session_name}.json"
            filepath = recordings_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(recording_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Recording saved to: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to save recording: {e}")
    
    def get_recordings(self) -> List[Dict[str, Any]]:
        """Get list of available recordings"""
        recordings = []
        recordings_dir = Path("recordings")
        
        if not recordings_dir.exists():
            return recordings
        
        for file_path in recordings_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    recordings.append({
                        'filename': file_path.name,
                        'session_name': data.get('session_name', 'Unknown'),
                        'duration': data.get('duration', 0),
                        'total_actions': data.get('total_actions', 0),
                        'created_at': data.get('created_at', 'Unknown')
                    })
            except Exception as e:
                self.logger.warning(f"Failed to read recording {file_path}: {e}")
        
        return recordings
    
    def load_recording(self, filename: str) -> Optional[Dict[str, Any]]:
        """Load a specific recording"""
        recordings_dir = Path("recordings")
        filepath = recordings_dir / filename
        
        if not filepath.exists():
            self.logger.error(f"Recording file not found: {filepath}")
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load recording: {e}")
            return None 