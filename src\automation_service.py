"""
Automation Service - Main service that coordinates data interface and Selenium automation
"""

import asyncio
import json
import logging
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from core.staging_automation import StagingAutomationEngine, AutomationResult

@dataclass
class AutomationJob:
    """Represents an automation job"""
    job_id: str
    selected_records: List[str]
    status: str  # 'pending', 'running', 'completed', 'failed'
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    results: List[AutomationResult] = None
    error_message: Optional[str] = None

class AutomationService:
    """Service that manages automation jobs and coordinates between UI and Selenium"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Job management
        self.jobs: Dict[str, AutomationJob] = {}
        self.current_job: Optional[AutomationJob] = None
        self.automation_engine: Optional[StagingAutomationEngine] = None
        
        # Threading
        self.automation_thread: Optional[threading.Thread] = None
        self.is_running = False
        
        # Setup logging
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging for the automation service"""
        log_file = Path("automation_service.log")
        
        # Create file handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.INFO)
    
    def start_automation_job(self, selected_record_ids: List[str]) -> str:
        """Start a new automation job"""
        try:
            # Generate job ID
            job_id = f"auto_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create job
            job = AutomationJob(
                job_id=job_id,
                selected_records=selected_record_ids,
                status='pending',
                created_at=datetime.now().isoformat(),
                results=[]
            )
            
            # Store job
            self.jobs[job_id] = job
            
            # Start automation in background thread
            self.automation_thread = threading.Thread(
                target=self._run_automation_job,
                args=(job_id,),
                daemon=True
            )
            self.automation_thread.start()
            
            self.logger.info(f"Started automation job {job_id} for {len(selected_record_ids)} records")
            return job_id
            
        except Exception as e:
            self.logger.error(f"Failed to start automation job: {e}")
            raise
    
    def _run_automation_job(self, job_id: str):
        """Run automation job in background thread"""
        try:
            job = self.jobs.get(job_id)
            if not job:
                self.logger.error(f"Job {job_id} not found")
                return
            
            # Update job status
            job.status = 'running'
            job.started_at = datetime.now().isoformat()
            self.current_job = job
            
            self.logger.info(f"Starting automation job {job_id}")
            
            # Run the automation
            asyncio.run(self._execute_automation_job(job))
            
        except Exception as e:
            self.logger.error(f"Error in automation job {job_id}: {e}")
            if job_id in self.jobs:
                self.jobs[job_id].status = 'failed'
                self.jobs[job_id].error_message = str(e)
                self.jobs[job_id].completed_at = datetime.now().isoformat()
        finally:
            self.current_job = None
    
    async def _execute_automation_job(self, job: AutomationJob):
        """Execute the automation job"""
        try:
            # Initialize automation engine
            self.automation_engine = StagingAutomationEngine(self.config)
            await self.automation_engine.initialize()
            
            # Fetch staging records for the selected IDs
            staging_records = await self._fetch_staging_records(job.selected_records)
            
            if not staging_records:
                raise Exception("No staging records found for selected IDs")
            
            self.logger.info(f"Processing {len(staging_records)} staging records")
            
            # Process the records
            results = await self.automation_engine.process_staging_records(staging_records)
            
            # Update job with results
            job.results = results
            job.status = 'completed'
            job.completed_at = datetime.now().isoformat()
            
            # Log summary
            successful = sum(1 for r in results if r.success)
            failed = len(results) - successful
            
            self.logger.info(f"Job {job.job_id} completed: {successful} successful, {failed} failed")
            
        except Exception as e:
            self.logger.error(f"Automation job {job.job_id} failed: {e}")
            job.status = 'failed'
            job.error_message = str(e)
            job.completed_at = datetime.now().isoformat()
            raise
        finally:
            # Cleanup automation engine
            if self.automation_engine:
                await self.automation_engine.cleanup()
                self.automation_engine = None
    
    async def _fetch_staging_records(self, record_ids: List[str]) -> List[Dict[str, Any]]:
        """Fetch staging records from API or mock data"""
        try:
            # For now, we'll create mock data based on the record IDs
            # In a real implementation, this would fetch from the actual API
            
            mock_records = []
            for record_id in record_ids:
                # Parse record ID to extract employee info
                parts = record_id.split('_')
                if len(parts) >= 2:
                    employee_id = parts[0]
                    date = parts[1] if len(parts) > 1 else '2025-06-10'
                else:
                    employee_id = record_id
                    date = '2025-06-10'
                
                # Create mock record
                mock_record = {
                    'id': record_id,
                    'employee_id': employee_id,
                    'employee_name': self._get_employee_name(employee_id),
                    'date': date,
                    'task_code': 'OC7190',
                    'station_code': 'STN-BLR',
                    'raw_charge_job': '(OC7190) BOILER OPERATION / STN-BLR (STATION BOILER) / BLR00000 (LABOUR COST) / L (LABOUR)',
                    'status': 'staged',
                    'hours': 7.0,
                    'unit': 1.0
                }
                
                mock_records.append(mock_record)
            
            self.logger.info(f"Created {len(mock_records)} mock staging records")
            return mock_records
            
        except Exception as e:
            self.logger.error(f"Failed to fetch staging records: {e}")
            return []
    
    def _get_employee_name(self, employee_id: str) -> str:
        """Get employee name from ID (mock implementation)"""
        # Mock employee mapping
        employee_map = {
            'POM00132': 'SEPTIAN ADE PRATAMA',
            'POM00181': 'ADE PRASETYA',
            'POM00190': 'RIDHO ANDYTHIA',
            'POM00194': 'MOCHAMAD FEBRY',
            'POM00197': 'Rivaldi Prasetyo'
        }
        
        return employee_map.get(employee_id, f'Employee {employee_id}')
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of an automation job"""
        job = self.jobs.get(job_id)
        if not job:
            return None
        
        # Convert results to dict format
        results_dict = []
        if job.results:
            for result in job.results:
                results_dict.append({
                    'success': result.success,
                    'record_id': result.record_id,
                    'message': result.message,
                    'error_details': result.error_details,
                    'processing_time': result.processing_time
                })
        
        return {
            'job_id': job.job_id,
            'status': job.status,
            'created_at': job.created_at,
            'started_at': job.started_at,
            'completed_at': job.completed_at,
            'selected_records_count': len(job.selected_records),
            'results': results_dict,
            'error_message': job.error_message
        }
    
    def get_all_jobs(self) -> List[Dict[str, Any]]:
        """Get status of all jobs"""
        return [self.get_job_status(job_id) for job_id in self.jobs.keys()]
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job"""
        job = self.jobs.get(job_id)
        if not job:
            return False
        
        if job.status == 'running':
            # In a real implementation, we would need to signal the automation thread to stop
            # For now, we'll just mark it as cancelled
            job.status = 'cancelled'
            job.completed_at = datetime.now().isoformat()
            self.logger.info(f"Job {job_id} cancelled")
            return True
        
        return False
    
    def cleanup_old_jobs(self, max_age_hours: int = 24):
        """Cleanup old completed jobs"""
        try:
            current_time = datetime.now()
            jobs_to_remove = []
            
            for job_id, job in self.jobs.items():
                if job.completed_at:
                    completed_time = datetime.fromisoformat(job.completed_at)
                    age_hours = (current_time - completed_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self.jobs[job_id]
                self.logger.info(f"Cleaned up old job {job_id}")
            
            if jobs_to_remove:
                self.logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old jobs: {e}")
    
    def get_current_job_status(self) -> Optional[Dict[str, Any]]:
        """Get status of currently running job"""
        if self.current_job:
            return self.get_job_status(self.current_job.job_id)
        return None
    
    def is_automation_running(self) -> bool:
        """Check if automation is currently running"""
        return self.current_job is not None and self.current_job.status == 'running'

# Global automation service instance
automation_service: Optional[AutomationService] = None

def get_automation_service(config: Dict[str, Any] = None) -> AutomationService:
    """Get or create the global automation service instance"""
    global automation_service
    
    if automation_service is None:
        if config is None:
            # Load default config
            config = {
                "browser": {
                    "headless": False,
                    "window_size": [1280, 720],
                    "disable_notifications": True
                },
                "automation": {
                    "implicit_wait": 10,
                    "page_load_timeout": 30,
                    "script_timeout": 30
                },
                "credentials": {
                    "username": "adm075",
                    "password": "adm075"
                },
                "urls": {
                    "login": "http://millwarep3:8004/",
                    "taskRegister": "http://millwarep3:8004/en/PR/trx/frmPrTrxTaskRegisterDet.aspx"
                }
            }
        
        automation_service = AutomationService(config)
    
    return automation_service
