"""
Browser Manager - Handles Chrome WebDriver setup and configuration
Provides centralized browser management for the automation application
"""

import os
import time
import logging
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager


class BrowserManager:
    """Manages Chrome WebDriver instance and configuration"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.driver = None
        self.logger = logging.getLogger(__name__)
        
        # Default browser options
        self.default_options = {
            'headless': False,
            'window_size': (1280, 720),
            'disable_notifications': True,
            'disable_popup_blocking': False,
            'disable_dev_shm_usage': True,
            'no_sandbox': False,
            'disable_extensions': False,
            'user_data_dir': None,
            'profile_directory': None,
            'download_directory': None,
            'user_agent': None,
            'language': 'en-US',
            'timezone': None,
            'viewport_size': None,
            'device_emulation': None,
            'proxy': None,
            'certificate_errors': False,
            'incognito': False,
            'disable_images': False,
            'disable_javascript': False,
            'page_load_strategy': 'normal',  # normal, eager, none
            'implicit_wait': 10,
            'page_load_timeout': 30,
            'script_timeout': 30,
            'enable_logging': True,
            'log_level': 'INFO'
        }

    def create_driver(self, options_override: Dict[str, Any] = None) -> webdriver.Chrome:
        """Create and configure Chrome WebDriver instance"""
        try:
            # Merge options
            merged_options = {**self.default_options, **self.config, **(options_override or {})}
            
            # Setup Chrome options
            chrome_options = self._setup_chrome_options(merged_options)
            
            # Setup Chrome service
            service = self._setup_chrome_service(merged_options)
            
            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Configure timeouts and settings
            self._configure_driver_settings(merged_options)
            
            # Set window size and position
            self._configure_window(merged_options)
            
            self.logger.info("Chrome WebDriver created successfully")
            return self.driver
            
        except Exception as e:
            self.logger.error(f"Failed to create Chrome WebDriver: {e}")
            raise

    def _setup_chrome_options(self, options: Dict[str, Any]) -> Options:
        """Setup Chrome options based on configuration"""
        chrome_options = Options()
        
        # Basic options
        if options.get('headless', False):
            chrome_options.add_argument('--headless=new')
        
        if options.get('disable_notifications', True):
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_experimental_option('prefs', {
                'profile.default_content_setting_values.notifications': 2
            })
        
        if options.get('disable_popup_blocking', False):
            chrome_options.add_argument('--disable-popup-blocking')
        
        if options.get('disable_dev_shm_usage', True):
            chrome_options.add_argument('--disable-dev-shm-usage')
        
        if options.get('no_sandbox', False):
            chrome_options.add_argument('--no-sandbox')
        
        if options.get('disable_extensions', False):
            chrome_options.add_argument('--disable-extensions')
        
        if options.get('incognito', False):
            chrome_options.add_argument('--incognito')
        
        # Certificate and security options
        if options.get('certificate_errors', False):
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        
        # User data directory and profile
        if options.get('user_data_dir'):
            chrome_options.add_argument(f'--user-data-dir={options["user_data_dir"]}')
        
        if options.get('profile_directory'):
            chrome_options.add_argument(f'--profile-directory={options["profile_directory"]}')
        
        # Language and locale
        if options.get('language'):
            chrome_options.add_argument(f'--lang={options["language"]}')
        
        # User agent
        if options.get('user_agent'):
            chrome_options.add_argument(f'--user-agent={options["user_agent"]}')
        
        # Proxy configuration
        if options.get('proxy'):
            proxy_config = options['proxy']
            if isinstance(proxy_config, str):
                chrome_options.add_argument(f'--proxy-server={proxy_config}')
            elif isinstance(proxy_config, dict):
                if proxy_config.get('http'):
                    chrome_options.add_argument(f'--proxy-server=http://{proxy_config["http"]}')
                elif proxy_config.get('socks5'):
                    chrome_options.add_argument(f'--proxy-server=socks5://{proxy_config["socks5"]}')
        
        # Performance and resource options
        if options.get('disable_images', False):
            chrome_options.add_experimental_option('prefs', {
                'profile.managed_default_content_settings.images': 2
            })
        
        if options.get('disable_javascript', False):
            chrome_options.add_experimental_option('prefs', {
                'profile.managed_default_content_settings.javascript': 2
            })
        
        # Download directory
        if options.get('download_directory'):
            chrome_options.add_experimental_option('prefs', {
                'download.default_directory': options['download_directory'],
                'download.prompt_for_download': False,
                'download.directory_upgrade': True,
                'safebrowsing.enabled': True
            })
        
        # Device emulation
        if options.get('device_emulation'):
            device_config = options['device_emulation']
            mobile_emulation = {
                'deviceMetrics': {
                    'width': device_config.get('width', 375),
                    'height': device_config.get('height', 667),
                    'pixelRatio': device_config.get('pixel_ratio', 2.0)
                },
                'userAgent': device_config.get('user_agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)')
            }
            chrome_options.add_experimental_option('mobileEmulation', mobile_emulation)
        
        # Page load strategy
        page_load_strategy = options.get('page_load_strategy', 'normal')
        chrome_options.page_load_strategy = page_load_strategy
        
        # Additional experimental options
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Disable blink features that might interfere
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # Enable logging if requested
        if options.get('enable_logging', True):
            log_level = options.get('log_level', 'INFO')
            chrome_options.add_argument(f'--log-level={log_level}')
            chrome_options.set_capability('goog:loggingPrefs', {
                'browser': log_level,
                'driver': log_level,
                'performance': log_level
            })
        
        return chrome_options

    def _setup_chrome_service(self, options: Dict[str, Any]) -> Service:
        """Setup Chrome service"""
        try:
            # Use ChromeDriverManager to automatically download and manage ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = Service(driver_path)
            
            # Configure service options
            if options.get('enable_logging', True):
                service.log_path = 'chromedriver.log'
            
            return service
            
        except Exception as e:
            self.logger.error(f"Failed to setup Chrome service: {e}")
            raise

    def _configure_driver_settings(self, options: Dict[str, Any]):
        """Configure driver timeouts and settings"""
        if not self.driver:
            return
        
        # Set timeouts
        implicit_wait = options.get('implicit_wait', 10)
        page_load_timeout = options.get('page_load_timeout', 30)
        script_timeout = options.get('script_timeout', 30)
        
        self.driver.implicitly_wait(implicit_wait)
        self.driver.set_page_load_timeout(page_load_timeout)
        self.driver.set_script_timeout(script_timeout)
        
        # Execute script to remove automation indicators
        try:
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
        except Exception:
            pass

    def _configure_window(self, options: Dict[str, Any]):
        """Configure browser window"""
        if not self.driver:
            return
        
        try:
            # Set window size
            window_size = options.get('window_size', (1280, 720))
            if window_size:
                self.driver.set_window_size(window_size[0], window_size[1])
            
            # Set viewport size if different from window size
            viewport_size = options.get('viewport_size')
            if viewport_size:
                self.driver.execute_script(f"""
                    window.resizeTo({viewport_size[0]}, {viewport_size[1]});
                """)
            
            # Maximize window if requested
            if options.get('maximize_window', False):
                self.driver.maximize_window()
            
            # Position window if specified
            window_position = options.get('window_position')
            if window_position:
                self.driver.set_window_position(window_position[0], window_position[1])
                
        except Exception as e:
            self.logger.warning(f"Failed to configure window: {e}")

    def create_driver_with_profile(self, profile_name: str = "AutoFill") -> webdriver.Chrome:
        """Create driver with specific Chrome profile"""
        profile_options = {
            'user_data_dir': os.path.expanduser(f'~/ChromeProfiles'),
            'profile_directory': profile_name
        }
        
        return self.create_driver(profile_options)

    def create_headless_driver(self) -> webdriver.Chrome:
        """Create headless Chrome driver for background operations"""
        headless_options = {
            'headless': True,
            'disable_notifications': True,
            'disable_popup_blocking': True,
            'disable_dev_shm_usage': True,
            'no_sandbox': True,
            'disable_images': True,
            'window_size': (1920, 1080)
        }
        
        return self.create_driver(headless_options)

    def create_mobile_driver(self, device_name: str = "iPhone 12") -> webdriver.Chrome:
        """Create driver with mobile device emulation"""
        device_configs = {
            "iPhone 12": {
                'width': 390,
                'height': 844,
                'pixel_ratio': 3.0,
                'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            },
            "iPad": {
                'width': 768,
                'height': 1024,
                'pixel_ratio': 2.0,
                'user_agent': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            },
            "Samsung Galaxy S21": {
                'width': 360,
                'height': 800,
                'pixel_ratio': 3.0,
                'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36'
            }
        }
        
        device_config = device_configs.get(device_name, device_configs["iPhone 12"])
        mobile_options = {
            'device_emulation': device_config
        }
        
        return self.create_driver(mobile_options)

    def get_driver(self) -> Optional[webdriver.Chrome]:
        """Get current driver instance"""
        return self.driver

    def is_driver_alive(self) -> bool:
        """Check if driver is still alive and responsive"""
        if not self.driver:
            return False
        
        try:
            # Try to get current URL to test if driver is responsive
            _ = self.driver.current_url
            return True
        except Exception:
            return False

    def restart_driver(self, options_override: Dict[str, Any] = None) -> webdriver.Chrome:
        """Restart the driver with same or new options"""
        self.quit_driver()
        return self.create_driver(options_override)

    def quit_driver(self):
        """Safely quit the driver"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("Chrome WebDriver quit successfully")
            except Exception as e:
                self.logger.warning(f"Error quitting driver: {e}")
            finally:
                self.driver = None

    def close_current_tab(self):
        """Close current tab"""
        if self.driver:
            try:
                self.driver.close()
            except Exception as e:
                self.logger.warning(f"Error closing tab: {e}")

    def switch_to_tab(self, tab_index: int = 0):
        """Switch to specific tab by index"""
        if self.driver:
            try:
                handles = self.driver.window_handles
                if 0 <= tab_index < len(handles):
                    self.driver.switch_to.window(handles[tab_index])
                else:
                    self.logger.warning(f"Tab index {tab_index} out of range")
            except Exception as e:
                self.logger.warning(f"Error switching to tab: {e}")

    def open_new_tab(self, url: str = None):
        """Open new tab and optionally navigate to URL"""
        if self.driver:
            try:
                self.driver.execute_script("window.open('');")
                self.switch_to_tab(-1)  # Switch to last tab
                
                if url:
                    self.driver.get(url)
            except Exception as e:
                self.logger.warning(f"Error opening new tab: {e}")

    def get_driver_info(self) -> Dict[str, Any]:
        """Get information about current driver"""
        if not self.driver:
            return {}
        
        try:
            info = {
                'current_url': self.driver.current_url,
                'title': self.driver.title,
                'window_handles': len(self.driver.window_handles),
                'window_size': self.driver.get_window_size(),
                'capabilities': dict(self.driver.capabilities),
                'session_id': self.driver.session_id
            }
            return info
        except Exception as e:
            self.logger.warning(f"Error getting driver info: {e}")
            return {}

    def take_screenshot(self, filename: str = None) -> str:
        """Take screenshot and return filename"""
        if not self.driver:
            return ""
        
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"
            
            success = self.driver.save_screenshot(filename)
            if success:
                self.logger.info(f"Screenshot saved: {filename}")
                return filename
            else:
                self.logger.warning("Failed to save screenshot")
                return ""
        except Exception as e:
            self.logger.warning(f"Error taking screenshot: {e}")
            return ""

    def get_page_source(self) -> str:
        """Get current page source"""
        if self.driver:
            try:
                return self.driver.page_source
            except Exception as e:
                self.logger.warning(f"Error getting page source: {e}")
        return ""

    def execute_script(self, script: str, *args) -> Any:
        """Execute JavaScript in browser"""
        if self.driver:
            try:
                return self.driver.execute_script(script, *args)
            except Exception as e:
                self.logger.warning(f"Error executing script: {e}")
        return None

    def clear_browser_data(self):
        """Clear browser data (cookies, cache, etc.)"""
        if self.driver:
            try:
                # Clear cookies
                self.driver.delete_all_cookies()
                
                # Clear local storage and session storage
                self.driver.execute_script("window.localStorage.clear();")
                self.driver.execute_script("window.sessionStorage.clear();")
                
                # Clear cache via DevTools (if available)
                try:
                    self.driver.execute_cdp_cmd('Network.clearBrowserCache', {})
                    self.driver.execute_cdp_cmd('Network.clearBrowserCookies', {})
                except Exception:
                    pass  # CDP commands might not be available
                    
                self.logger.info("Browser data cleared")
            except Exception as e:
                self.logger.warning(f"Error clearing browser data: {e}")

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.quit_driver() 