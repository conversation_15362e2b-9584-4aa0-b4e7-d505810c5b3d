"""
Stale Element Handler - Handle stale element reference exceptions
Provides robust element interaction with automatic retry mechanisms
"""

import time
import logging
from typing import Optional, Any, Callable, Dict
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException


class StaleElementHandler:
    """Handle stale element reference exceptions with automatic retry"""
    
    def __init__(self, driver, max_retries: int = 3, retry_delay: float = 0.5):
        self.driver = driver
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = logging.getLogger(__name__)
    
    def with_stale_retry(self, operation: Callable, *args, **kwargs) -> Any:
        """
        Execute operation with automatic stale element retry
        
        Args:
            operation: Function to execute (e.g., element.click, element.send_keys)
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Result of the operation
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return operation(*args, **kwargs)
                
            except StaleElementReferenceException as e:
                last_exception = e
                self.logger.warning(f"Stale element reference on attempt {attempt + 1}/{self.max_retries + 1}")
                
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay)
                    # Try to re-find the element if it's the first argument and is a WebElement
                    if args and isinstance(args[0], WebElement):
                        # Get element info before it becomes stale
                        try:
                            tag_name = args[0].tag_name
                            element_id = args[0].get_attribute('id')
                            element_class = args[0].get_attribute('class')
                            element_name = args[0].get_attribute('name')
                            
                            # Try to re-find the element
                            new_element = self._refind_element(tag_name, element_id, element_class, element_name)
                            if new_element:
                                # Replace the stale element with the new one
                                args = (new_element,) + args[1:]
                                self.logger.info("Successfully re-found stale element")
                                continue
                        except:
                            pass
                else:
                    self.logger.error(f"Max retries exceeded for stale element operation")
                    raise last_exception
        
        raise last_exception
    
    def _refind_element(self, tag_name: str, element_id: str, element_class: str, element_name: str) -> Optional[WebElement]:
        """Try to re-find an element using various strategies"""
        
        # Strategy 1: Find by ID
        if element_id:
            try:
                return self.driver.find_element(By.ID, element_id)
            except:
                pass
        
        # Strategy 2: Find by name
        if element_name:
            try:
                return self.driver.find_element(By.NAME, element_name)
            except:
                pass
        
        # Strategy 3: Find by class and tag combination
        if element_class and tag_name:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, f"{tag_name}.{element_class.split()[0]}")
                if elements:
                    return elements[0]
            except:
                pass
        
        # Strategy 4: Find by tag name (less specific, get first match)
        if tag_name:
            try:
                elements = self.driver.find_elements(By.TAG_NAME, tag_name)
                if elements:
                    return elements[0]
            except:
                pass
        
        return None
    
    def safe_click(self, element: WebElement) -> bool:
        """Safely click an element with stale element retry"""
        try:
            element.click()
            return True
        except StaleElementReferenceException:
            self.logger.warning("Stale element on click, retrying...")
            time.sleep(self.retry_delay)
            return False
        except Exception as e:
            self.logger.error(f"Failed to click element: {e}")
            return False
    
    def safe_send_keys(self, element: WebElement, keys: str, clear_first: bool = False) -> bool:
        """Safely send keys to an element with stale element retry"""
        try:
            if clear_first:
                element.clear()
            element.send_keys(keys)
            return True
        except StaleElementReferenceException:
            self.logger.warning("Stale element on send_keys, retrying...")
            time.sleep(self.retry_delay)
            return False
        except Exception as e:
            self.logger.error(f"Failed to send keys to element: {e}")
            return False
    
    def safe_get_text(self, element: WebElement) -> str:
        """Safely get text from an element with stale element retry"""
        try:
            return self.with_stale_retry(lambda: element.text)
        except Exception as e:
            self.logger.error(f"Failed to get text from element after retries: {e}")
            return ""
    
    def safe_get_attribute(self, element: WebElement, attribute: str) -> str:
        """Safely get attribute from an element with stale element retry"""
        try:
            return self.with_stale_retry(element.get_attribute, attribute)
        except Exception as e:
            self.logger.error(f"Failed to get attribute from element after retries: {e}")
            return ""
    
    def safe_is_displayed(self, element: WebElement) -> bool:
        """Safely check if element is displayed with stale element retry"""
        try:
            return self.with_stale_retry(lambda: element.is_displayed())
        except Exception as e:
            self.logger.error(f"Failed to check element visibility after retries: {e}")
            return False
    
    def wait_for_element_to_be_stable(self, element: WebElement, timeout: int = 5) -> bool:
        """Wait for an element to be stable (not changing) before interaction"""
        try:
            # Wait a bit for any dynamic changes to settle
            time.sleep(0.5)
            
            # Check if element is still attached to DOM
            try:
                _ = element.is_displayed()  # This will raise StaleElementReferenceException if stale
                return True
            except StaleElementReferenceException:
                return False
                
        except Exception as e:
            self.logger.warning(f"Element stability check failed: {e}")
            return False
    
    def robust_element_interaction(self, selector: str, action: str, value: str = None, 
                                  timeout: int = 10, selector_type: str = 'css') -> bool:
        """
        Perform robust element interaction with automatic retry and re-finding
        
        Args:
            selector: Element selector
            action: Action to perform ('click', 'send_keys', 'clear')
            value: Value for send_keys action
            timeout: Timeout for finding element
            selector_type: Type of selector ('css', 'xpath', 'id', 'name')
        
        Returns:
            True if successful, False otherwise
        """
        
        for attempt in range(self.max_retries + 1):
            try:
                # Find the element
                element = self._find_element_by_type(selector, selector_type, timeout)
                if not element:
                    self.logger.error(f"Element not found: {selector}")
                    return False
                
                # Wait for element to be stable
                if not self.wait_for_element_to_be_stable(element):
                    self.logger.warning(f"Element became stale before interaction: {selector}")
                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay)
                        continue
                    return False
                
                # Perform the action
                if action == 'click':
                    return self.safe_click(element)
                elif action == 'send_keys':
                    return self.safe_send_keys(element, value or "")
                elif action == 'clear':
                    try:
                        self.with_stale_retry(element.clear)
                        return True
                    except:
                        return False
                elif action == 'get_text':
                    return self.safe_get_text(element)
                elif action == 'get_attribute':
                    return self.safe_get_attribute(element, value or "")
                else:
                    self.logger.error(f"Unknown action: {action}")
                    return False
                    
            except StaleElementReferenceException:
                self.logger.warning(f"Stale element on interaction attempt {attempt + 1}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return False
            except Exception as e:
                self.logger.error(f"Element interaction failed: {e}")
                return False
        
        return False
    
    def _find_element_by_type(self, selector: str, selector_type: str, timeout: int) -> Optional[WebElement]:
        """Find element by specified selector type"""
        
        wait = WebDriverWait(self.driver, timeout)
        
        try:
            if selector_type == 'css':
                return wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
            elif selector_type == 'xpath':
                return wait.until(EC.presence_of_element_located((By.XPATH, selector)))
            elif selector_type == 'id':
                return wait.until(EC.presence_of_element_located((By.ID, selector)))
            elif selector_type == 'name':
                return wait.until(EC.presence_of_element_located((By.NAME, selector)))
            elif selector_type == 'class':
                return wait.until(EC.presence_of_element_located((By.CLASS_NAME, selector)))
            elif selector_type == 'tag':
                return wait.until(EC.presence_of_element_located((By.TAG_NAME, selector)))
            else:
                # Default to CSS
                return wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                
        except TimeoutException:
            return None
        except Exception as e:
            self.logger.error(f"Error finding element: {e}")
            return None 