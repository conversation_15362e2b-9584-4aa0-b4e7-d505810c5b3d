"""
Data Interface - Flask Web Application
Provides a web interface for viewing and selecting staging data for automation
"""

import json
import logging
import requests
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import pandas as pd

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))
from automation_service import get_automation_service

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
API_BASE_URL = "http://localhost:5173"
STAGING_DATA_ENDPOINT = f"{API_BASE_URL}/api/staging/data"

class StagingDataManager:
    """Manages staging data operations"""
    
    def __init__(self):
        self.cached_data = None
        self.cache_timestamp = None
        self.cache_duration = 300  # 5 minutes
    
    def fetch_staging_data(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Fetch staging data from API with optional filters"""
        try:
            # Check cache first
            if self._is_cache_valid():
                logger.info("Using cached staging data")
                return self._apply_filters(self.cached_data, filters)
            
            # Fetch fresh data
            logger.info(f"Fetching staging data from {STAGING_DATA_ENDPOINT}")
            
            params = {}
            if filters:
                # Convert filters to API parameters
                if filters.get('employee_name'):
                    params['employee_name'] = filters['employee_name']
                if filters.get('date_from'):
                    params['date_from'] = filters['date_from']
                if filters.get('date_to'):
                    params['date_to'] = filters['date_to']
                if filters.get('status'):
                    params['status'] = filters['status']
                if filters.get('limit'):
                    params['limit'] = filters['limit']
                if filters.get('offset'):
                    params['offset'] = filters['offset']
            
            response = requests.get(STAGING_DATA_ENDPOINT, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Cache the data
            self.cached_data = data
            self.cache_timestamp = datetime.now()
            
            logger.info(f"Successfully fetched {len(data.get('data', []))} staging records")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching staging data: {e}")
            return {
                'error': f'Failed to fetch data from API: {str(e)}',
                'data': [],
                'total': 0,
                'page': 1,
                'per_page': 50
            }
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {
                'error': f'Unexpected error: {str(e)}',
                'data': [],
                'total': 0,
                'page': 1,
                'per_page': 50
            }
    
    def _is_cache_valid(self) -> bool:
        """Check if cached data is still valid"""
        if not self.cached_data or not self.cache_timestamp:
            return False
        
        age = (datetime.now() - self.cache_timestamp).total_seconds()
        return age < self.cache_duration
    
    def _apply_filters(self, data: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Apply client-side filters to cached data"""
        if not filters or not data.get('data'):
            return data
        
        filtered_records = data['data']
        
        # Apply employee name filter
        if filters.get('employee_name'):
            employee_filter = filters['employee_name'].lower()
            filtered_records = [
                record for record in filtered_records
                if employee_filter in record.get('employee_name', '').lower()
            ]
        
        # Apply date range filter
        if filters.get('date_from') or filters.get('date_to'):
            date_from = filters.get('date_from')
            date_to = filters.get('date_to')
            
            filtered_records = [
                record for record in filtered_records
                if self._is_date_in_range(record.get('date'), date_from, date_to)
            ]
        
        # Apply status filter
        if filters.get('status'):
            status_filter = filters['status']
            filtered_records = [
                record for record in filtered_records
                if record.get('status') == status_filter
            ]
        
        # Update data with filtered results
        result = data.copy()
        result['data'] = filtered_records
        result['total'] = len(filtered_records)
        
        return result
    
    def _is_date_in_range(self, record_date: str, date_from: str, date_to: str) -> bool:
        """Check if record date is within specified range"""
        try:
            if not record_date:
                return False
            
            # Parse record date (assuming YYYY-MM-DD format)
            record_dt = datetime.strptime(record_date, '%Y-%m-%d')
            
            if date_from:
                from_dt = datetime.strptime(date_from, '%Y-%m-%d')
                if record_dt < from_dt:
                    return False
            
            if date_to:
                to_dt = datetime.strptime(date_to, '%Y-%m-%d')
                if record_dt > to_dt:
                    return False
            
            return True
            
        except ValueError:
            logger.warning(f"Invalid date format: {record_date}")
            return False

# Initialize data manager and automation service
staging_manager = StagingDataManager()
automation_service = get_automation_service()

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/api/staging-data')
def get_staging_data():
    """API endpoint to get staging data with filters"""
    try:
        # Get filter parameters
        filters = {
            'employee_name': request.args.get('employee_name'),
            'date_from': request.args.get('date_from'),
            'date_to': request.args.get('date_to'),
            'status': request.args.get('status', 'staged'),
            'limit': request.args.get('limit', 50, type=int),
            'offset': request.args.get('offset', 0, type=int)
        }
        
        # Remove None values
        filters = {k: v for k, v in filters.items() if v is not None}
        
        # Fetch data
        data = staging_manager.fetch_staging_data(filters)
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f"Error in get_staging_data: {e}")
        return jsonify({
            'error': str(e),
            'data': [],
            'total': 0
        }), 500

@app.route('/api/process-selected', methods=['POST'])
def process_selected_records():
    """API endpoint to trigger automation for selected records"""
    try:
        data = request.get_json()
        selected_ids = data.get('selected_ids', [])

        if not selected_ids:
            return jsonify({'error': 'No records selected'}), 400

        logger.info(f"Processing {len(selected_ids)} selected records")

        # Check if automation is already running
        if automation_service.is_automation_running():
            return jsonify({'error': 'Automation is already running'}), 409

        # Start automation job
        job_id = automation_service.start_automation_job(selected_ids)

        result = {
            'success': True,
            'message': f'Started automation for {len(selected_ids)} records',
            'selected_count': len(selected_ids),
            'automation_id': job_id
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error processing selected records: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/employees')
def get_employees():
    """Get unique employee names for filter dropdown"""
    try:
        data = staging_manager.fetch_staging_data()
        
        if data.get('error'):
            return jsonify({'error': data['error']}), 500
        
        # Extract unique employee names
        employees = set()
        for record in data.get('data', []):
            if record.get('employee_name'):
                employees.add(record['employee_name'])
        
        return jsonify(sorted(list(employees)))
        
    except Exception as e:
        logger.error(f"Error getting employees: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/job-status/<job_id>')
def get_job_status(job_id):
    """Get status of a specific automation job"""
    try:
        status = automation_service.get_job_status(job_id)

        if not status:
            return jsonify({'error': 'Job not found'}), 404

        return jsonify(status)

    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/jobs')
def get_all_jobs():
    """Get status of all automation jobs"""
    try:
        jobs = automation_service.get_all_jobs()
        return jsonify(jobs)

    except Exception as e:
        logger.error(f"Error getting jobs: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/current-job')
def get_current_job():
    """Get status of currently running job"""
    try:
        current_job = automation_service.get_current_job_status()

        if not current_job:
            return jsonify({'message': 'No job currently running'}), 200

        return jsonify(current_job)

    except Exception as e:
        logger.error(f"Error getting current job: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/cancel-job/<job_id>', methods=['POST'])
def cancel_job(job_id):
    """Cancel a running automation job"""
    try:
        success = automation_service.cancel_job(job_id)

        if success:
            return jsonify({'message': f'Job {job_id} cancelled successfully'})
        else:
            return jsonify({'error': 'Job not found or cannot be cancelled'}), 400

    except Exception as e:
        logger.error(f"Error cancelling job: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

if __name__ == '__main__':
    logger.info("Starting Data Interface Web Application")
    logger.info(f"API Endpoint: {STAGING_DATA_ENDPOINT}")
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
