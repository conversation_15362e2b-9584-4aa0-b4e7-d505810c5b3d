<tbody><tr class="mb-c">
    <td class="tdChildLabel">
                            Transaction Date :<span class="RedText">*</span>
                        </td>
    <td class="tdChildInput">
                            <input name="ctl00$MainContent$txtTrxDate" type="text" value="10/06/2025" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$txtTrxDate\',\'\')', 0)" onkeypress="if (WebForm_TextBoxKeyHandler(event) == false) return false;" id="MainContent_txtTrxDate" style="width:200px;">
                            
                            <span id="MainContent_rvtxtTrxDate" style="color:Red;display:none;"><br>Current valid date is 1/6/2025 To 30/6/2025 </span>
                            <span id="MainContent_rvftxtTrxDate" type="Date" style="color:Red;display:none;"><br>Please key in Date</span>
                           
                        </td>
    <td class="tdMiddleSplit">
                        </td>
    <td class="tdChildLabel">
                        </td>
    <td class="tdChildInput">
                        </td>
    <td class="tdMiddleSplit">
                        </td>
</tr>
<tr class="mb-c">
    <td class="tdChildLabel">
                            Employee :<span class="RedText">*</span>
                        </td>
    <td colspan="5">
                            <select name="ctl00$MainContent$ddlEmployee" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$ddlEmployee\',\'\')', 0)" id="MainContent_ddlEmployee" style="width: 300px; display: none;">
        <option value=""></option>
        <option value="POM00020">POM00020(Tigo)</option>
        <option value="POM00022">POM00022(Ramadani)</option>
        <option value="POM00023">POM00023(Arianto)</option>
        <option value="POM00024">POM00024(Husaini)</option>
        <option value="POM00026">POM00026(Nursamsi)</option>
        <option value="POM00028">POM00028(Derry)</option>
        <option value="POM00029">POM00029(Yulizar)</option>
        <option value="POM00030">POM00030(Zurianto)</option>
        <option value="POM00033">POM00033(Supandi)</option>
        <option value="POM00034">POM00034(Andison Supriyanto)</option>
        <option value="POM00036">POM00036(Ichsanudin)</option>
        <option value="POM00038">POM00038(Pordiman Samosir)</option>
        <option value="POM00040">POM00040(dedy Burhansyah)</option>
        <option value="POM00041">POM00041(Adi Apriadi)</option>
        <option value="POM00042">POM00042(Sudiar)</option>
        <option value="POM00043">POM00043(Najmi)</option>
        <option value="POM00046">POM00046(Dedi)</option>
        <option value="POM00048">POM00048(Hendra Winardi)</option>
        <option value="POM00051">POM00051(Suhartomo)</option>
        <option value="POM00053">POM00053(Sofiyan)</option>
        <option value="POM00054">POM00054(Atmo Haryo)</option>
        <option value="POM00055">POM00055(Asmadi)</option>
        <option value="POM00061">POM00061(Mediarta)</option>
        <option value="POM00062">POM00062(Pujiono)</option>
        <option value="POM00063">POM00063(Hanip)</option>
        <option value="POM00064">POM00064(Andri Iswadi)</option>
        <option value="POM00065">POM00065(Dick Havianto)</option>
        <option value="POM00066">POM00066(Eko Juniyansah)</option>
        <option value="POM00067">POM00067(Suharjono)</option>
        <option value="POM00068">POM00068(Rastanto)</option>
        <option value="POM00071">POM00071(Ariadi)</option>
        <option value="POM00073">POM00073(bintara)</option>
        <option value="POM00076">POM00076(Helmi)</option>
        <option value="POM00078">POM00078(Slamet Widodo)</option>
        <option value="POM00079">POM00079(Beni Apriyanto)</option>
        <option value="POM00081">POM00081(Erwanto)</option>
        <option value="POM00087">POM00087(Jaya Saputra)</option>
        <option value="POM00088">POM00088(Dafid Iwan Saputra)</option>
        <option value="POM00092">POM00092(Riky Maryanto Susanto)</option>
        <option value="POM00093">POM00093(Ibnu Wardana)</option>
        <option value="POM00094">POM00094(Siaga Pratama)</option>
        <option value="POM00095">POM00095(Bambang Supriyono)</option>
        <option value="POM00101">POM00101(Marja Isnata)</option>
        <option value="POM00102">POM00102(Ari Widodo)</option>
        <option value="POM00103">POM00103(Tri Bowono Yudo)</option>
        <option value="POM00104">POM00104(Darma Setiawan)</option>
        <option value="POM00106">POM00106(Agus Irawan)</option>
        <option value="POM00109">POM00109(LIGA FIRDANA)</option>
        <option value="POM00114">POM00114(ANWAR)</option>
        <option value="POM00115">POM00115(SAMSIR)</option>
        <option value="POM00120">POM00120(EDDO PRATHAMA)</option>
        <option value="POM00121">POM00121(RENDY GUSTOYO)</option>
        <option value="POM00123">POM00123(PANDU PRATAMA PURSA)</option>
        <option value="POM00124">POM00124(GALUH FERDIANJAYA)</option>
        <option value="POM00125">POM00125(ASWANDI)</option>
        <option value="POM00127">POM00127(MUHAMAD KHALIQ)</option>
        <option value="POM00128">POM00128(DEFI FANTERA)</option>
        <option value="POM00130">POM00130(SYAFRILLIYANSYAH)</option>
        <option value="POM00132">POM00132(SEPTIAN ADE PRATAMA)</option>
        <option value="POM00135">POM00135(IRWAN)</option>
        <option value="POM00137">POM00137(TAUFIK HIDAYAT)</option>
        <option value="POM00138">POM00138(ANDRIANO SANOPRIANSYAH)</option>
        <option value="POM00139">POM00139(ANDRI JULIANDRI)</option>
        <option value="POM00140">POM00140(ASQALANY)</option>
        <option value="POM00143">POM00143(REZA WAHYUDI)</option>
        <option value="POM00144">POM00144(Danang Budi Santoso)</option>
        <option value="POM00145">POM00145(Aturi)</option>
        <option value="POM00146">POM00146(Januarzy)</option>
        <option value="POM00149">POM00149(SYAHRIL)</option>
        <option value="POM00151">POM00151(ARI YANTO)</option>
        <option value="POM00158">POM00158(MAMAN ERYANDI)</option>
        <option value="POM00159">POM00159(ANDRIANSYAH)</option>
        <option value="POM00160">POM00160(HERYANDI)</option>
        <option value="POM00162">POM00162(PERLY FEBRIANTO)</option>
        <option value="POM00163">POM00163(JUMHADI)</option>
        <option value="POM00164">POM00164(CHERPIANSYAH)</option>
        <option value="POM00165">POM00165(AZRUL KHAN)</option>
        <option value="POM00168">POM00168(DAVID FIRDAUS)</option>
        <option value="POM00173">POM00173(DENI ARDIANSAH)</option>
        <option value="POM00174">POM00174(KIKI FIRMANSYAH)</option>
        <option value="POM00175">POM00175(RAJU ARISTO)</option>
        <option value="POM00176">POM00176(ISBANDI)</option>
        <option value="POM00177">POM00177(Fedrico Diazta)</option>
        <option value="POM00179">POM00179(RENDY NOPRIYANDI)</option>
        <option value="POM00180">POM00180(ARI WIDODO)</option>
        <option selected="selected" value="POM00181">POM00181(ADE PRASETYA)</option>
        <option value="POM00182">POM00182(FERDY ARLIANSYAH)</option>
        <option value="POM00183">POM00183(AGUS RIYADI)</option>
        <option value="POM00184">POM00184(FIKRIANSYAH)</option>
        <option value="POM00186">POM00186(SULTHONIK PELUBI)</option>
        <option value="POM00188">POM00188(HARRY NUGROHO PRABOWO)</option>
        <option value="POM00190">POM00190(RIDHO ANDYTHIA)</option>
        <option value="POM00192">POM00192(RIO)</option>
        <option value="POM00194">POM00194(MOCHAMAD FEBRY)</option>
        <option value="POM00197">POM00197(Rivaldi Prasetyo)</option>
        <option value="POM00199">POM00199(OKTORIO DWI SAKTI)</option>
        <option value="POM00204">POM00204(RIZKY)</option>
        <option value="POM00207">POM00207(FAIZAL)</option>
        <option value="POM00208">POM00208(GUNTUR)</option>
        <option value="POM00209">POM00209(JOKO PRIONO)</option>
        <option value="POM00210">POM00210(WANDA ANDIKA)</option>
        <option value="POM00211">POM00211(TAUPIK HIDAYAT)</option>
        <option value="POM00213">POM00213(ADYKA)</option>
        <option value="POM00214">POM00214(AGUS SETIAWAN)</option>
        <option value="POM00215">POM00215(WAHYU PRATAMA)</option>
        <option value="POM00216">POM00216(CHARYADI)</option>
        <option value="POM00217">POM00217(SUHERMAN SAPUTRA)</option>
        <option value="POM00218">POM00218(FERDI PRIHANDA)</option>
        <option value="POM00219">POM00219(TIKI BASISTA)</option>
        <option value="POM00220">POM00220(RETNO SUGIARTO)</option>
        <option value="POM00221">POM00221(MAMAN SURYADI)</option>
        <option value="POM00223">POM00223(AGUSTIAN)</option>
        <option value="POM00224">POM00224(ABU HURAIROH)</option>
        <option value="POM00226">POM00226(RASAT)</option>
        <option value="pom00227">pom00227(MAHMUDI)</option>
        <option value="POM00229">POM00229(HERU SURADI)</option>
        <option value="POM00235">POM00235(Risky Sumantri)</option>
        <option value="POM00236">POM00236(DEDY PRASAPTA)</option>
        <option value="POM00237">POM00237(RIZKY SUGANDA)</option>
        <option value="POM00238">POM00238(LIO PRATAMA)</option>
        <option value="POM00239">POM00239(ERWIN SUSANTO)</option>
        <option value="POM00240">POM00240(HENDRA JAYA)</option>
        <option value="POM00241">POM00241(AGUSTIAWAN)</option>
        <option value="POM00242">POM00242(ARDIANSYAH)</option>
        <option value="POM00244">POM00244(SABANI WARUWU)</option>
        <option value="POM00245">POM00245(RENDY)</option>
        <option value="POM00246">POM00246(FEBRIAN SAPUTRA)</option>
        <option value="POM00247">POM00247(RIZKY ABDILLAH)</option>
        <option value="POM00253">POM00253(ARYAT DHIMI OCTOVAN)</option>
        <option value="POM00254">POM00254(RIZAL SAHRONI)</option>
        <option value="POM00255">POM00255(SUWARI)</option>
        <option value="POM00256">POM00256(AZMAR)</option>
        <option value="POM00258">POM00258(RIDHO PURNAMA)</option>
        <option value="POM00259">POM00259(WAHYU YULIANTO)</option>
        <option value="POM00260">POM00260(IHSAN ALFAHRY)</option>
        <option value="POM00261">POM00261(DEDI)</option>
        <option value="POM00262">POM00262(PANJI MEKA BUANA)</option>
        <option value="POM00263">POM00263(DEKA SETIAWAN)</option>
        <option value="POM00265">POM00265(HERLY HIDAYAT)</option>
        <option value="POM00266">POM00266(ALDI WINATA)</option>
        <option value="POM00267">POM00267(MUHAMMAD RIDHO AKBAR PAPILAYA)</option>
        <option value="POM00268">POM00268(MAXIMA CAHYANI)</option>
        <option value="POM00269">POM00269(HANDREAN WAHYU NUGROHO)</option>
        <option value="POM00270">POM00270(BINTANG PUJIYONO)</option>
        <option value="POM00271">POM00271(KELVIN AGUSTIANTO)</option>
        <option value="POM00272">POM00272(ANGGY)</option>
        <option value="POM00273">POM00273(VIRANDA)</option>
        <option value="POM00274">POM00274(KARNO PUTRA)</option>
        <option value="POM00275">POM00275(EKO RAMADHAN)</option>
        <option value="POM00276">POM00276(REZA AGUSTIAN)</option>
        <option value="POM00277">POM00277(MAUKAR)</option>
        <option value="POM00278">POM00278(SELAMAT ALAMSYAH)</option>
        <option value="POM00281">POM00281(PUTERI DIEKA AFRIANI)</option>
        <option value="POM00282">POM00282(DHEA AGATHA PASARIBU)</option>
        <option value="POM00283">POM00283(ALDI)</option>
        <option value="POM00284">POM00284(VOLIOKA AURORA)</option>
        <option value="POM00285">POM00285(JEKI RISTIANDI)</option>
        <option value="POM00286">POM00286(ANUGRAH PRATAMA PUTRA)</option>
        <option value="POM00287">POM00287(AAN  KRIS WANDA)</option>
        <option value="POM00288">POM00288(YOSSI PRANATA)</option>
        <option value="POM00289">POM00289(FUJI PRASTAMA)</option>
        <option value="POM00290">POM00290(SEPTIAN PRATAMA)</option>
        <option value="POM00294">POM00294(MUHAMMAD SALMAN ALFARIZI)</option>
        <option value="SUP00017">SUP00017(CV. Bumi Karya Kampit)</option>

    </select><input class="ui-autocomplete-input ui-widget ui-widget-content" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true" style="width: 298.4px; height: 17px;"><button type="button" style="display: none" tabindex="-1" title="Show All Items" class="ui-button ui-widget ui-state-default ui-button-icon-only ui-button-icon" role="button" aria-disabled="false"><span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"></span><span class="ui-button-text">&nbsp;</span></button>
                            <span id="MainContent_ddlEmployee_RFV" style="color:Red;display:none;"><br>Please select Employee</span>
                            
                        </td>
</tr>
<tr class="mb-c">
    <td class="tdChildLabel">
                            Transaction Type :<span class="RedText">*</span>
                        </td>
    <td colspan="5">
                            <table id="MainContent_rblOT">
        <tbody><tr>
            <td><input id="MainContent_rblOT_0" type="radio" name="ctl00$MainContent$rblOT" value="1" checked="checked"><label for="MainContent_rblOT_0">Normal</label></td><td><input id="MainContent_rblOT_1" type="radio" name="ctl00$MainContent$rblOT" value="2" onclick="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$rblOT$1\',\'\')', 0)"><label for="MainContent_rblOT_1">Overtime</label></td>
        </tr>
    </tbody></table>
                        </td>
</tr>
<tr class="mb-c">
    <td class="tdChildLabel">
                            Shift
                        </td>
    <td colspan="5">
                            <select name="ctl00$MainContent$ddlShift" id="MainContent_ddlShift" style="width: 300px">

    </select>
                            <span id="MainContent_ddlShift_RFV" style="color:Red;display:none;"><br>Please select Shift</span>
                        </td>
</tr>
<tr class="mb-c">
    <td class="tdChildLabel">
                            Task Code :<span class="RedText">*</span>
                        </td>
    <td colspan="5">
                            <select name="ctl00$MainContent$ddlTaskCode" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$ddlTaskCode\',\'\')', 0)" id="MainContent_ddlTaskCode" style="width: 300px; display: none;">
        <option value="">Select Task Code</option>
        <option value="CA4814">(CA4814) C/Roll Wages (Compos)</option>
        <option value="GA9010">(GA9010) VEHICLE RUNNING</option>
        <option value="GA9050">(GA9050) WORKSHOP CONTROL ACCOUNT</option>
        <option value="GA9127">(GA9127) (AL) PERSONNEL SICK LEAVE</option>
        <option value="GA9130">(GA9130) PERSONNEL ANNUAL LEAVE</option>
        <option value="GA9234">(GA9234) UPKEEP OF BUILDINGS</option>
        <option value="OC7110">(OC7110) FRUIT RECEPTION AND STORAGE</option>
        <option value="OC7120">(OC7120) STERILIZER OPERATION</option>
        <option value="OC7130">(OC7130) THRESHING STATION OPERATION</option>
        <option value="OC7140">(OC7140) EFB HANDLING OPERATION</option>
        <option value="OC7150">(OC7150) EFB PREBREAKER STATION </option>
        <option value="OC7160">(OC7160) PRESS OPERATION</option>
        <option value="OC7170">(OC7170) CLARIFICATION OPERATION</option>
        <option value="OC7180">(OC7180) KERNEL OPERATION</option>
        <option selected="selected" value="OC7190">(OC7190) BOILER OPERATION</option>
        <option value="OC7200">(OC7200) ENGINE ROOM OPERATION</option>
        <option value="OC7210">(OC7210) CPO STORAGE OPERATION</option>
        <option value="OC7220">(OC7220) EFFLUENT TREATMENT PLANT</option>
        <option value="OC7230">(OC7230) WATER PLANT OPERATION</option>
        <option value="OC7240">(OC7240) LABORATORY ANALYSIS</option>
        <option value="OC7250">(OC7250) WORKSHOP MAINTENANCE</option>
        <option value="OC7260">(OC7260) GENERAL WAREHOUSE</option>
        <option value="OC7319">(OC7319) UPKEEP OF WHELL LOADER</option>
        <option value="OC7320">(OC7320) UPKEEP OF FACTORY </option>
        <option value="OC7321">(OC7321) UPKEEP OF WEIGHBRIDGE</option>

    </select><input class="ui-autocomplete-input ui-widget ui-widget-content" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true" style="width: 298.4px; height: 17px;"><button type="button" style="display: none" tabindex="-1" title="Show All Items" class="ui-button ui-widget ui-state-default ui-button-icon-only ui-button-icon" role="button" aria-disabled="false"><span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"></span><span class="ui-button-text">&nbsp;</span></button>
                            <span id="MainContent_ddlTaskCode_RFV" style="color:Red;display:none;"><br>Please select Task Code</span>
                        </td>
</tr>
<tr id="MainContent_trAccCode" class="mb-c">
    <td colspan="6">
                            <span id="MainContent_MultiDimAcc_lblErr"></span>
<table id="MainContent_MultiDimAcc_tbAccount" style="width:100%; margin: 0px 0px 0px 0px; border: 0px;">
        <tbody><tr id="MainContent_MultiDimAcc_trBlkCode">
            <td id="MainContent_MultiDimAcc_tdBlkCode" height="25" width="18.6%" valign="top">
    <span id="MainContent_MultiDimAcc_lblBlkCode">Station Code</span>:<span class="RedText">*</span>
</td>
            <td style="padding-left: 0" valign="top">
    <select name="ctl00$MainContent$MultiDimAcc$ddlBlock" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$MultiDimAcc$ddlBlock\',\'\')', 0)" id="MainContent_MultiDimAcc_ddlBlock" style="width: 300px; display: none;">
                <option value="">Select Code</option>
                <option selected="selected" value="STN-BLR">STN-BLR (STATION BOILER)</option>
                <option value="STN-MFS">STN-MFS (STATION MOVING FLOOR SYSTEM)</option>

            </select><input class="ui-autocomplete-input ui-widget ui-widget-content" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true" style="width: 298.4px; height: 17px;"><button type="button" style="display: none" tabindex="-1" title="Show All Items" class="ui-button ui-widget ui-state-default ui-button-icon-only ui-button-icon" role="button" aria-disabled="false"><span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"></span><span class="ui-button-text">&nbsp;</span></button>
    <span id="MainContent_MultiDimAcc_reqValBlock" class="ErrorMessage" style="color:Red;display:none;"><br>Please select &lt;%#lblBlkCode.Text %&gt; code.</span>
</td>
        </tr>
        <tr id="MainContent_MultiDimAcc_trSubBlkCode">
            <td id="MainContent_MultiDimAcc_tdSubBlkCode" height="25" width="18.6%" valign="top">
    <span id="MainContent_MultiDimAcc_lblSubBlk">Machine Code</span>:<span class="RedText">*</span>
</td>
            <td style="padding-left: 0" valign="top">
    <select name="ctl00$MainContent$MultiDimAcc$ddlSubBlk" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$MultiDimAcc$ddlSubBlk\',\'\')', 0)" id="MainContent_MultiDimAcc_ddlSubBlk" style="width: 300px; display: none;">
                <option selected="selected" value="">Select Code</option>
                <option value="BLR00000">BLR00000 (LABOUR COST)</option>
                <option value="BLR00001">BLR00001 (Genset)</option>
                <option value="BLR00002">BLR00002 (Solar Untuk Pembakaran Awal Boiler)</option>
                <option value="BLR01001">BLR01001 (Shell Conveyor Under Shell Hopper)</option>
                <option value="BLR02001">BLR02001 (Inclined Shell Conveyor)</option>
                <option value="BLR03001">BLR03001 (Horizontal Fiber Shell Conveyor)</option>
                <option value="BLR04001">BLR04001 (Inclined Fiber Shell Scraper Bar Conveyor)</option>
                <option value="BLR05001">BLR05001 (Boiler Fuel Feeding Scraper Bar Conveyor &nbsp;)</option>
                <option value="BLR06001">BLR06001 (Boiler Fuel Return Elevator &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR07001">BLR07001 (Softener Tank No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR08001">BLR08001 (Softener Tank No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR09001">BLR09001 (Feed Water Tank c/w Accessories)</option>
                <option value="BLR10001">BLR10001 (Daerator Tank c/w Accessories &nbsp;&nbsp;)</option>
                <option value="BLR11001">BLR11001 (Daerator Tank Feed Pump No.1 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR12001">BLR12001 (Daerator Tank Feed Pump No.2 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR13001">BLR13001 (Daerator Extraction Pump No.1 &nbsp;&nbsp;)</option>
                <option value="BLR14001">BLR14001 (Daerator Extraction Pump No.2 &nbsp;&nbsp;)</option>
                <option value="BLR15001">BLR15001 (Vacuum Daerator Tank No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR16001">BLR16001 (Vacuum Daerator Tank No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR17001">BLR17001 (Vacuum Daerator Pump No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR18001">BLR18001 (Vacuum Daerator Pump No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR19001">BLR19001 (Steam Turbine Feed Water Pump &nbsp;&nbsp;)</option>
                <option value="BLR20001">BLR20001 (Feed Water Pump No.1 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR21001">BLR21001 (Feed Water Pump No.2 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR22001">BLR22001 (Feed Water Pump No.3 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR23001">BLR23001 (Feed Water Pump No.4 &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR24001">BLR24001 (Chemical Dozing Pump No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR25001">BLR25001 (Chemical Dozing Pump No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR26001">BLR26001 (Chemical Tank Stirrer No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR27001">BLR27001 (Chemical Tank Stirrer No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28001">BLR28001 (BOILER No.1 (Boiler Body, Furnace, Doors, Ash Bin, Ducting, Dumper.)</option>
                <option value="BLR28002">BLR28002 (BOILER No.1 (Steam Drum, Bottom Drum, Header, Water Control Modulating. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28003">BLR28003 (BOILER No.1 (Main Steam Valve, Safety Valve, Pressure Gauge, Water Level Control, Temperator Gauge, etc. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28004">BLR28004 (BOILER No.1 (Fuel Auger Conveyor No.1 &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28005">BLR28005 (BOILER No.1 (Fuel Auger Conveyor No.2 &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28006">BLR28006 (BOILER No.1 (Modulating Damper &nbsp;)</option>
                <option value="BLR28007">BLR28007 (BOILER No.1 (Fuel Forced Fan &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28008">BLR28008 (BOILER No.1 (Forced Draft Fan &nbsp;&nbsp;)</option>
                <option value="BLR28009">BLR28009 (BOILER No.1 (Secondary Fan &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28010">BLR28010 (BOILER No.1 (Induced Draft Fan &nbsp;)</option>
                <option value="BLR28011">BLR28011 (BOILER No.1 (Air Compressor &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28012">BLR28012 (BOILER No.1 (Main Ash Conveyor &nbsp;)</option>
                <option value="BLR28013">BLR28013 (BOILER No.1 (Ash Fly Conveyor &nbsp;&nbsp;)</option>
                <option value="BLR28014">BLR28014 (BOILER No.1 (Ash Conveyor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28015">BLR28015 (BOILER No.1 (Soot Conveyor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28016">BLR28016 (BOILER No.1 (Dust Colector Airlock No.1 &nbsp;&nbsp;)</option>
                <option value="BLR28017">BLR28017 (BOILER No.1 (Dust Colector Airlock No.2 &nbsp;&nbsp;)</option>
                <option value="BLR28018">BLR28018 (BOILER No.1 (Chimney &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR28019">BLR28019 (BOILER No.1 (Ash Store.)</option>
                <option value="BLR29001">BLR29001 (BOILER No.2 (Boiler Body, Furnace, Doors, Ash Bin, Ducting, Dumper.)</option>
                <option value="BLR29002">BLR29002 (BOILER No.2 (Steam Super Heater. &nbsp;&nbsp;)</option>
                <option value="BLR29003">BLR29003 (BOILER No.2 (Steam Drum, Bottom Drum, Header, Water Control Modulating. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29004">BLR29004 (BOILER No.2 (Main Steam Valve, Safety Valve, Pressure Gauge, Water Level Control, Temperator Gauge, etc. &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29005">BLR29005 (BOILER No.2 (Fuel Auger Conveyor No.1 &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29006">BLR29006 (BOILER No.2 (Fuel Auger Conveyor No.2 &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29007">BLR29007 (BOILER No.2 (Modulating Damper &nbsp;)</option>
                <option value="BLR29008">BLR29008 (BOILER No.2 (Fuel Forced Fan &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29009">BLR29009 (BOILER No.2 (Forced Draft Fan &nbsp;&nbsp;)</option>
                <option value="BLR29010">BLR29010 (BOILER No.2 (Secondary Fan &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29011">BLR29011 (BOILER No.2 (Induced Draft Fan &nbsp;)</option>
                <option value="BLR29012">BLR29012 (BOILER No.2 (Air Compressor &nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29013">BLR29013 (BOILER No.2 (Vibrating Grate No.1 &nbsp;)</option>
                <option value="BLR29014">BLR29014 (BOILER No.2 (Vibrating Grate No.2 &nbsp;)</option>
                <option value="BLR29015">BLR29015 (BOILER No.2 (Main Ash Conveyor No.1 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29016">BLR29016 (BOILER No.2 (Main Ash Conveyor No.2 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29017">BLR29017 (BOILER No.2 (Ash Fly Conveyor No.1)</option>
                <option value="BLR29018">BLR29018 (BOILER No.2 (Ash Fly Conveyor No.2)</option>
                <option value="BLR29019">BLR29019 (BOILER No.2 (Dust Conveyor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29020">BLR29020 (BOILER No.2 (Submerget Conveyor)</option>
                <option value="BLR29021">BLR29021 (BOILER No.2 (Dust Colector Airlock No.1 &nbsp;&nbsp;)</option>
                <option value="BLR29022">BLR29022 (BOILER No.2 (Dust Colector Airlock No.2 &nbsp;&nbsp;)</option>
                <option value="BLR29023">BLR29023 (BOILER No.2 (Chimney &nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR29024">BLR29024 (BOILER No.2 (Ash Store.)</option>
                <option value="BLR30001">BLR30001 (Building and Support Structure, Flate form c/w Accessories&nbsp;)</option>
                <option value="BLR31001">BLR31001 (ELECTRICAL (MCCB Boiler Station (Contactor, Overload Relay, Timer, Soft Starter, Inverter, etc) &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR31002">BLR31002 (ELECTRICAL (Pnuematic Instrument Control c/w Fitting Equipment and Accessories &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR31003">BLR31003 (ELECTRICAL (Lighting and Fitting Equipment &nbsp;&nbsp;)</option>
                <option value="BLR32001">BLR32001 (CLEANING &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR33001">BLR33001 (Inclined Wet Shell Disposal Conveyor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</option>
                <option value="BLR34001">BLR34001 (Conveyor Return Fuel Screw)</option>

            </select><input class="ui-autocomplete-input ui-widget ui-widget-content" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true" style="width: 298.4px; height: 17px;"><button type="button" style="display: none" tabindex="-1" title="Show All Items" class="ui-button ui-widget ui-state-default ui-button-icon-only ui-button-icon" role="button" aria-disabled="false"><span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"></span><span class="ui-button-text">&nbsp;</span></button>
    <span id="MainContent_MultiDimAcc_reqValSubBlk" class="ErrorMessage" style="color:Red;display:none;"><br>Please select &lt;%#lblSubBlk.Text %&gt; code.</span>
</td>
        </tr>
        <tr id="MainContent_MultiDimAcc_trExpCode">
            <td height="25" width="18.6%" valign="top">
    <span id="MainContent_MultiDimAcc_lblExpCode">Expense Code</span>:<span class="RedText">*</span>
</td>
            <td style="padding-left: 0" valign="top">
    <select name="ctl00$MainContent$MultiDimAcc$ddlExpCode" onchange="javascript:setTimeout('__doPostBack(\'ctl00$MainContent$MultiDimAcc$ddlExpCode\',\'\')', 0)" id="MainContent_MultiDimAcc_ddlExpCode" style="width: 300px; display: none;">
                <option value="">Select Code</option>
                <option selected="selected" value="L">L (LABOUR)</option>

            </select><input class="ui-autocomplete-input ui-widget ui-widget-content" autocomplete="off" role="textbox" aria-autocomplete="list" aria-haspopup="true" style="width: 298.4px; height: 17px;"><button type="button" style="display: none" tabindex="-1" title="Show All Items" class="ui-button ui-widget ui-state-default ui-button-icon-only ui-button-icon" role="button" aria-disabled="false"><span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"></span><span class="ui-button-text">&nbsp;</span></button>
    <span id="MainContent_MultiDimAcc_reqValExpCode" class="ErrorMessage" style="color:Red;display:none;"><br>Please select &lt;%#lblExpCode.Text %&gt; code.</span>
</td>
        </tr>
    </tbody></table>
    






                        </td>
</tr>
<tr id="MainContent_trHours" class="mb-c">
    <td valign="top" class="tdChildLabel">
                            Hours :<span class="RedText">*</span>
                        </td>
    <td valign="top" colspan="5">
                            <input name="ctl00$MainContent$txtHours" type="text" value="7.0" maxlength="4" id="MainContent_txtHours" onkeyup="javascript:CheckHours()" style="width:100px;">
                            <span id="MainContent_revHours" style="color:Red;display:none;">Maximum length 4 digits and 1 decimal points with 0 and 5. </span>
                            <span id="MainContent_txtHours_RFV" style="color:Red;display:none;"><br>Please key in Hours</span>
                            <span id="MainContent_txtHours_CV" style="color:Red;display:none;">Please enter numeric value!</span>
                            <span id="MainContent_RVtxtHours" style="color:Red;display:none;">Please key in 0 to 24</span>
                            <span id="MainContent_CompareValidator3" style="color:Red;display:none;"><br>Please key in Hours,0 is Not Allow. </span>
                            <span id="errHours" style="display: none; color: red;">working hours / OT hours exceed limit!</span>

                        </td>
</tr>
<tr id="MainContent_tr1" class="mb-c">
    <td class="tdChildLabel">
                            Unit :<span class="RedText">*</span>
                        </td>
    <td colspan="5">
                            <input name="ctl00$MainContent$txtUnit" type="text" value="1" maxlength="8" id="MainContent_txtUnit" onkeyup="javascript:calUnitAmount();" style="width:100px;">
                            <span id="MainContent_lblTaskCodeUOM"></span>
                            <span id="MainContent_txtUnit_RFV" style="color:Red;display:none;"><br>Please key in Unit</span>
                            <span id="MainContent_txtUnit_CV" style="color:Red;display:none;"><br>Please enter numeric value</span>
                            <span id="MainContent_txtUnit_RV" style="color:Red;display:none;">Units must be greater or equal to 0</span>
                            <span id="MainContent_CompareValidator2" style="color:Red;display:none;"><br>Please key in Unit,0 is Not Allow. </span>
                        </td>
</tr>
<tr>
    <td class="tdChildLabel">Rate :<span class="RedText">*</span></td>
    <td colspan="5">
                            <input name="ctl00$MainContent$txtUnitRate" type="text" value="0" maxlength="8" id="MainContent_txtUnitRate" disabled="disabled" class="aspNetDisabled" onkeyup="javascript:calUnitAmount();" style="width:100px;">
                            <span id="MainContent_txtUntRate_RFV" type="Currency" style="color:Red;display:none;"><br>Please key in Rate</span>
                           
                        </td>
</tr>
<tr>
    <td class="tdChildLabel">Amount :</td>
    <td colspan="5"><span id="MainContent_lblUnitAmountVal" style="text-align: left">0.00</span></td>
</tr>
<tr id="MainContent_trUnit" class="mb-c">
    <td valign="top" class="tdChildLabel">
                        </td>
    <td valign="top" class="tdChildInput">
                        </td>
    <td valign="middle" align="center" class="tdMiddleSplit">
                        </td>
    <td valign="top" class="tdChildLabel">
                        </td>
    <td valign="middle" align="center" class="tdChildLabel">
                        </td>
    <td valign="top" class="tdMiddleSplit">
                        </td>
</tr>
<tr class="mb-c">
    <td colspan="6">
                            <input type="submit" name="ctl00$MainContent$btnAdd" value="Add" onclick="return(DoValidation());WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$MainContent$btnAdd&quot;, &quot;&quot;, true, &quot;&quot;, &quot;&quot;, false, false))" id="MainContent_btnAdd" class="button">
                            
                            
                            
                        </td>
</tr>
</tbody>