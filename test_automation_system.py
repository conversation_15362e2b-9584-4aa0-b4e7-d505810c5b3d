#!/usr/bin/env python3
"""
Test Script for Automated Data Entry System
Tests both the data interface and automation components
"""

import sys
import time
import requests
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_data_interface():
    """Test the data interface API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Data Interface API...")
    
    try:
        # Test 1: Get staging data
        print("  📊 Testing staging data endpoint...")
        response = requests.get(f"{base_url}/api/staging-data", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"    ✅ Got {len(data.get('data', []))} staging records")
        else:
            print(f"    ❌ Failed: {response.status_code} - {response.text}")
            return False
        
        # Test 2: Get employees
        print("  👥 Testing employees endpoint...")
        response = requests.get(f"{base_url}/api/employees", timeout=10)
        
        if response.status_code == 200:
            employees = response.json()
            print(f"    ✅ Got {len(employees)} unique employees")
        else:
            print(f"    ❌ Failed: {response.status_code} - {response.text}")
            return False
        
        # Test 3: Get current job (should be none initially)
        print("  🔄 Testing current job endpoint...")
        response = requests.get(f"{base_url}/api/current-job", timeout=10)
        
        if response.status_code == 200:
            print("    ✅ Current job endpoint working")
        else:
            print(f"    ❌ Failed: {response.status_code} - {response.text}")
            return False
        
        # Test 4: Process selected records (mock test)
        print("  🚀 Testing process selected endpoint...")
        test_payload = {
            "selected_ids": ["test_record_1", "test_record_2"]
        }
        
        response = requests.post(
            f"{base_url}/api/process-selected",
            json=test_payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get('automation_id')
            print(f"    ✅ Started test automation job: {job_id}")
            
            # Test 5: Check job status
            print("  📋 Testing job status endpoint...")
            time.sleep(2)  # Give job time to start
            
            response = requests.get(f"{base_url}/api/job-status/{job_id}", timeout=10)
            if response.status_code == 200:
                status = response.json()
                print(f"    ✅ Job status: {status.get('status')}")
            else:
                print(f"    ❌ Job status failed: {response.status_code}")
                return False
                
        else:
            print(f"    ❌ Failed: {response.status_code} - {response.text}")
            return False
        
        print("  ✅ All data interface tests passed!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("    ❌ Connection failed - is the web interface running?")
        return False
    except Exception as e:
        print(f"    ❌ Test failed: {e}")
        return False

def test_automation_service():
    """Test the automation service components"""
    print("🤖 Testing Automation Service...")
    
    try:
        # Test automation service import
        print("  📦 Testing automation service import...")
        from automation_service import get_automation_service
        print("    ✅ Automation service imported successfully")
        
        # Test staging automation import
        print("  🎯 Testing staging automation import...")
        from core.staging_automation import StagingAutomationEngine
        print("    ✅ Staging automation imported successfully")
        
        # Test configuration loading
        print("  ⚙️ Testing configuration loading...")
        config = {
            "browser": {"headless": True},
            "credentials": {"username": "test", "password": "test"},
            "urls": {"login": "http://example.com"}
        }
        
        automation_service = get_automation_service(config)
        print("    ✅ Automation service initialized successfully")
        
        print("  ✅ All automation service tests passed!")
        return True
        
    except ImportError as e:
        print(f"    ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Test failed: {e}")
        return False

def test_dependencies():
    """Test that all required dependencies are available"""
    print("📦 Testing Dependencies...")
    
    required_packages = [
        'selenium',
        'webdriver_manager',
        'flask',
        'flask_cors',
        'requests',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    print("  ✅ All dependencies available!")
    return True

def test_configuration():
    """Test configuration files"""
    print("⚙️ Testing Configuration...")
    
    config_dir = Path(__file__).parent / "config"
    
    # Test app_config.json
    app_config_file = config_dir / "app_config.json"
    if app_config_file.exists():
        try:
            with open(app_config_file, 'r') as f:
                config = json.load(f)
            print("  ✅ app_config.json loaded successfully")
            
            # Check required sections
            required_sections = ['browser', 'automation', 'credentials', 'urls']
            for section in required_sections:
                if section in config:
                    print(f"    ✅ {section} section found")
                else:
                    print(f"    ❌ {section} section missing")
                    return False
                    
        except json.JSONDecodeError as e:
            print(f"  ❌ app_config.json invalid JSON: {e}")
            return False
    else:
        print("  ⚠️ app_config.json not found (will use defaults)")
    
    print("  ✅ Configuration tests passed!")
    return True

def test_file_structure():
    """Test that all required files and directories exist"""
    print("📁 Testing File Structure...")
    
    base_path = Path(__file__).parent
    
    required_files = [
        "src/core/automation_engine.py",
        "src/core/staging_automation.py",
        "src/core/browser_manager.py",
        "src/data_interface/app.py",
        "src/data_interface/templates/index.html",
        "src/automation_service.py",
        "run_automation_system.py",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = base_path / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing files: {len(missing_files)}")
        return False
    
    print("  ✅ All required files present!")
    return True

def main():
    """Run all tests"""
    print("🧪 AUTOMATED DATA ENTRY SYSTEM - TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Dependencies", test_dependencies),
        ("Configuration", test_configuration),
        ("Automation Service", test_automation_service),
        ("Data Interface", test_data_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name.upper()}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! System is ready to use.")
        print("\nTo start the system:")
        print("  python run_automation_system.py")
        print("\nWeb interface will be available at:")
        print("  http://localhost:5000")
        return True
    else:
        print(f"\n❌ {total - passed} tests failed. Please fix issues before running the system.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
